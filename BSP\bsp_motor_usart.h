/*
 * ========================================================================
 * 文件名称: bsp_motor_usart.h
 * 文件描述: 电机控制串口BSP层头文件
 * 功能说明: 提供电机控制板通信的底层串口驱动接口
 *
 * 硬件配置:
 * - 使用UART1进行电机控制板通信
 * - 支持单字节和多字节数据发送
 * - 中断方式接收数据处理
 *
 * 主要功能:
 * 1. 电机控制串口初始化
 * 2. 单字节数据发送
 * 3. 数组数据批量发送
 * 4. 接收数据中断处理
 *
 * 通信协议:
 * - 与电机控制板进行双向通信
 * - 发送控制命令到电机驱动板
 * - 接收电机状态和编码器数据
 *
 * 作者: [作者信息]
 * 创建日期: [创建日期]
 * 版本: V1.0
 * ========================================================================
 */

#ifndef __BSP_MOTOR_USART_H_
#define __BSP_MOTOR_USART_H_

// 包含TI官方驱动库配置头文件
#include "ti_msp_dl_config.h"
// 包含电机控制应用层头文件
#include "app_motor_usart.h"

/**
 * @brief 电机控制串口初始化函数
 * @param void 无参数
 * @return void 无返回值
 *
 * @description
 * 初始化用于电机控制通信的UART1串口
 *
 * @note
 * - 配置UART1的通信参数
 * - 使能接收中断
 * - 为电机控制通信做准备
 */
void Motor_Usart_init(void);

/**
 * @brief 电机控制串口发送单字节函数
 * @param Data 要发送的字节数据
 * @return void 无返回值
 *
 * @description
 * 通过UART1向电机控制板发送一个字节的数据
 *
 * @note
 * - 使用忙等待方式确保发送完成
 * - 主要用于发送控制命令和参数
 *
 * @example
 * Send_Motor_U8(0x55);  // 发送字节0x55到电机控制板
 */
void Send_Motor_U8(uint8_t Data);

/**
 * @brief 电机控制串口发送数组函数
 * @param pData 指向要发送数据数组的指针
 * @param Length 要发送的数据长度
 * @return void 无返回值
 *
 * @description
 * 通过UART1向电机控制板发送指定长度的数据数组
 *
 * @note
 * - 逐字节发送数组中的所有数据
 * - 适用于发送完整的控制命令或数据包
 * - 内部调用Send_Motor_U8函数实现
 *
 * @example
 * uint8_t cmd[] = {0x55, 0xAA, 0x01, 0x02};
 * Send_Motor_ArrayU8(cmd, 4);  // 发送4字节命令
 */
void Send_Motor_ArrayU8(uint8_t *pData, uint16_t Length);

#endif /* __BSP_MOTOR_USART_H_ */
