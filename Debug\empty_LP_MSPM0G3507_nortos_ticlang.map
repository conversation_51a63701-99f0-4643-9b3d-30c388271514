******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul  2 14:55:28 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000259


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000002c0  0001fd40  R  X
  SRAM                  20200000   00008000  00000283  00007d7d  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000002c0   000002c0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000001e8   000001e8    r-x .text
  000002a8    000002a8    00000018   00000018    r-- .cinit
20200000    20200000    00000083   00000000    rw-
  20200000    20200000    00000083   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000001e8     
                  000000c0    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000015a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000015c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000001d8    00000044     usart.o (.text.UART0_IRQHandler)
                  0000021c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000258    00000028            : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000280    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00000292    00000002     empty.o (.text.main)
                  00000294    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000029c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000002a0    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000002a4    00000004            : exit.c.obj (.text:abort)

.cinit     0    000002a8    00000018     
                  000002a8    00000008     (.cinit..data.load) [load image, compression = lzss]
                  000002b0    00000008     (__TI_handler_table)
                  000002b8    00000008     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000083     UNINITIALIZED
                  20200000    00000080     usart.o (.data.recv0_buff)
                  20200080    00000002     usart.o (.data.recv0_length)
                  20200082    00000001     usart.o (.data.recv0_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      192       0      
                                                              
    .\BSP\
       usart.o                        68     0         131    
    +--+------------------------------+------+---------+---------+
       Total:                         68     0         131    
                                                              
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         404    0         0      
                                                              
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      24        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   488    216       643    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000002b8 records: 1, size/record: 8, table size: 8
	.data: load addr=000002a8, load size=00000008 bytes, run addr=20200000, run size=00000083 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000002b0 records: 2, size/record: 4, table size: 8
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000015b  ADC0_IRQHandler               
0000015b  ADC1_IRQHandler               
0000015b  AES_IRQHandler                
000002a4  C$$EXIT                       
0000015b  CANFD0_IRQHandler             
0000015b  DAC0_IRQHandler               
0000015b  DMA_IRQHandler                
0000015b  Default_Handler               
0000015b  GROUP0_IRQHandler             
0000015b  GROUP1_IRQHandler             
0000015b  HardFault_Handler             
0000015b  I2C0_IRQHandler               
0000015b  I2C1_IRQHandler               
0000015b  NMI_Handler                   
0000015b  PendSV_Handler                
0000015b  RTC_IRQHandler                
0000029d  Reset_Handler                 
0000015b  SPI0_IRQHandler               
0000015b  SPI1_IRQHandler               
0000015b  SVC_Handler                   
0000015b  SysTick_Handler               
0000015b  TIMA0_IRQHandler              
0000015b  TIMA1_IRQHandler              
0000015b  TIMG0_IRQHandler              
0000015b  TIMG12_IRQHandler             
0000015b  TIMG6_IRQHandler              
0000015b  TIMG7_IRQHandler              
0000015b  TIMG8_IRQHandler              
000001d9  UART0_IRQHandler              
0000015b  UART1_IRQHandler              
0000015b  UART2_IRQHandler              
0000015b  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000002b8  __TI_CINIT_Base               
000002c0  __TI_CINIT_Limit              
000002c0  __TI_CINIT_Warm               
000002b0  __TI_Handler_Table_Base       
000002b8  __TI_Handler_Table_Limit      
0000021d  __TI_auto_init_nobinit_nopinit
0000015d  __TI_decompress_lzss          
00000281  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000295  __aeabi_memcpy                
00000295  __aeabi_memcpy4               
00000295  __aeabi_memcpy8               
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000259  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000002a1  _system_pre_init              
000002a5  abort                         
ffffffff  binit                         
00000000  interruptVectors              
00000293  main                          
000000c1  memcpy                        
20200000  recv0_buff                    
20200082  recv0_flag                    
20200080  recv0_length                  


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  memcpy                        
0000015b  ADC0_IRQHandler               
0000015b  ADC1_IRQHandler               
0000015b  AES_IRQHandler                
0000015b  CANFD0_IRQHandler             
0000015b  DAC0_IRQHandler               
0000015b  DMA_IRQHandler                
0000015b  Default_Handler               
0000015b  GROUP0_IRQHandler             
0000015b  GROUP1_IRQHandler             
0000015b  HardFault_Handler             
0000015b  I2C0_IRQHandler               
0000015b  I2C1_IRQHandler               
0000015b  NMI_Handler                   
0000015b  PendSV_Handler                
0000015b  RTC_IRQHandler                
0000015b  SPI0_IRQHandler               
0000015b  SPI1_IRQHandler               
0000015b  SVC_Handler                   
0000015b  SysTick_Handler               
0000015b  TIMA0_IRQHandler              
0000015b  TIMA1_IRQHandler              
0000015b  TIMG0_IRQHandler              
0000015b  TIMG12_IRQHandler             
0000015b  TIMG6_IRQHandler              
0000015b  TIMG7_IRQHandler              
0000015b  TIMG8_IRQHandler              
0000015b  UART1_IRQHandler              
0000015b  UART2_IRQHandler              
0000015b  UART3_IRQHandler              
0000015d  __TI_decompress_lzss          
000001d9  UART0_IRQHandler              
00000200  __STACK_SIZE                  
0000021d  __TI_auto_init_nobinit_nopinit
00000259  _c_int00_noargs               
00000281  __TI_decompress_none          
00000293  main                          
00000295  __aeabi_memcpy                
00000295  __aeabi_memcpy4               
00000295  __aeabi_memcpy8               
0000029d  Reset_Handler                 
000002a1  _system_pre_init              
000002a4  C$$EXIT                       
000002a5  abort                         
000002b0  __TI_Handler_Table_Base       
000002b8  __TI_CINIT_Base               
000002b8  __TI_Handler_Table_Limit      
000002c0  __TI_CINIT_Limit              
000002c0  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  recv0_buff                    
20200080  recv0_length                  
20200082  recv0_flag                    
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[76 symbols]
