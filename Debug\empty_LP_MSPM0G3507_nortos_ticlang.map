******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul  2 15:09:50 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003601


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00003c48  0001c3b8  R  X
  SRAM                  20200000   00008000  0000108d  00006f73  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003c48   00003c48    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003860   00003860    r-x .text
  00003920    00003920    000002a0   000002a0    r-- .rodata
  00003bc0    00003bc0    00000088   00000088    r-- .cinit
20200000    20200000    00000e8d   00000000    rw-
  20200000    20200000    00000800   00000000    rw- .sysmem
  20200800    20200800    00000430   00000000    rw- .bss
  20200c30    20200c30    0000025d   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003860     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000448     app_motor_usart.o (.text.Deal_data_real)
                  00000ed8    000003b8     libc.a : strtod.c.obj (.text.strtod)
                  00001290    00000220            : _printfi.c.obj (.text._pconv_a)
                  000014b0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  0000168c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000181e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001820    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000195c    00000120            : _printfi.c.obj (.text._pconv_e)
                  00001a7c    00000114            : memory.c.obj (.text.aligned_alloc)
                  00001b90    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001c9c    000000f8     libc.a : fputs.c.obj (.text.fputs)
                  00001d94    000000e8            : memory.c.obj (.text.free)
                  00001e7c    000000e8     empty.o (.text.main)
                  00001f64    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002048    000000e0     libc.a : setvbuf.c.obj (.text.setvbuf)
                  00002128    000000d8            : s_scalbn.c.obj (.text.scalbn)
                  00002200    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000022a2    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000233c    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00002340    00000088            : strcmp-armv6m.S.obj (.text:strcmp)
                  000023c8    0000007c            : fclose.c.obj (.text.__TI_closefile)
                  00002444    0000007c            : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000024c0    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002534    0000000c                            : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00002540    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000025b4    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002624    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00002694    0000006c     libsysbm.a : hostlseek.c.obj (.text.HOSTlseek)
                  00002700    0000006c                : hostrename.c.obj (.text.HOSTrename)
                  0000276c    0000006c     libc.a : fseek.c.obj (.text.fseeko)
                  000027d8    0000006c            : getdevice.c.obj (.text.getdevice)
                  00002844    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000028ac    00000068     app_motor_usart.o (.text.send_motor_PID)
                  00002914    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000297a    00000064            : _io_perm.c.obj (.text.__TI_wrt_ok)
                  000029de    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000029e0    00000064     libc.a : memory.c.obj (.text.split)
                  00002a44    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002aa6    00000062     libc.a : memset16.S.obj (.text:memset)
                  00002b08    00000060     libsysbm.a : hostopen.c.obj (.text.HOSTopen)
                  00002b68    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00002bc4    0000005c            : printf.c.obj (.text.printf)
                  00002c20    00000058     libsysbm.a : hostread.c.obj (.text.HOSTread)
                  00002c78    00000058                : hostwrite.c.obj (.text.HOSTwrite)
                  00002cd0    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00002d28    00000058            : _printfi.c.obj (.text._pconv_f)
                  00002d80    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00002dd6    00000052     libc.a : fflush.c.obj (.text.__TI_doflush)
                  00002e28    00000052            : _printfi.c.obj (.text._ecpy)
                  00002e7a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002e7c    00000050     libsysbm.a : close.c.obj (.text.close)
                  00002ecc    0000004c     app_motor_usart.o (.text.Contrl_Speed)
                  00002f18    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002f62    00000002     libc.a : _lock.c.obj (.text._nop)
                  00002f64    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002fac    00000048     libsysbm.a : hostclose.c.obj (.text.HOSTclose)
                  00002ff4    00000048                : hostunlink.c.obj (.text.HOSTunlink)
                  0000303c    00000048     app_motor_usart.o (.text.send_upload_data)
                  00003084    00000048     libc.a : strtok.c.obj (.text.strtok)
                  000030cc    00000044     usart.o (.text.UART0_IRQHandler)
                  00003110    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003150    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003190    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000031d0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003210    00000040     delay.o (.text.delay_ms)
                  00003250    00000040     app_motor_usart.o (.text.send_motor_deadzone)
                  00003290    00000040     app_motor_usart.o (.text.send_motor_type)
                  000032d0    00000040     app_motor_usart.o (.text.send_pulse_line)
                  00003310    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000334c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003388    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000033c2    00000002     --HOLE-- [fill = 0]
                  000033c4    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  000033fc    00000034            : fopen.c.obj (.text.__TI_cleanup)
                  00003430    00000034     libsysbm.a : trgmsg.c.obj (.text.__TI_readmsg)
                  00003464    00000034                : trgmsg.c.obj (.text.__TI_writemsg)
                  00003498    00000034     libc.a : exit.c.obj (.text.exit)
                  000034cc    00000034            : getdevice.c.obj (.text.finddevice)
                  00003500    00000030            : _printfi.c.obj (.text._fcpy)
                  00003530    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000355c    0000002c     libsysbm.a : unlink.c.obj (.text.unlink)
                  00003588    00000028     libc.a : memory.c.obj (.text.free_list_insert)
                  000035b0    00000028     libsysbm.a : lseek.c.obj (.text.lseek)
                  000035d8    00000028                : write.c.obj (.text.write)
                  00003600    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003628    00000026            : strspn.c.obj (.text.strspn)
                  0000364e    00000002     --HOLE-- [fill = 0]
                  00003650    00000024     usart.o (.text.USART_Init)
                  00003674    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00003698    00000024     libc.a : fputs.c.obj (.text.puts)
                  000036bc    00000024            : strcspn.c.obj (.text.strcspn)
                  000036e0    00000022            : memccpy.c.obj (.text.memccpy)
                  00003702    00000002     --HOLE-- [fill = 0]
                  00003704    00000020     usart.o (.text.fputc)
                  00003724    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003742    00000002     --HOLE-- [fill = 0]
                  00003744    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003760    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000377c    0000001c     libc.a : memory.c.obj (.text.free_list_remove)
                  00003798    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000037b0    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000037c8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000037dc    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000037f0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003802    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00003814    00000010            : wcslen.c.obj (.text.wcslen)
                  00003824    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003834    0000000e            : memory.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003842    00000002     --HOLE-- [fill = 0]
                  00003844    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00003852    0000000e     libsysbm.a : hostrename.c.obj (.text.strcpy)
                  00003860    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000386e    0000000e     libsysbm.a : hostrename.c.obj (.text.strlen)
                  0000387c    0000000c     libc.a : memory.c.obj (.text.malloc)
                  00003888    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003892    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000389c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000038ac    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000038b6    0000000a            : sprintf.c.obj (.text._outc)
                  000038c0    00000008            : strtod.c.obj (.text.OUTLINED_FUNCTION_0)
                  000038c8    00000008            : strtod.c.obj (.text.OUTLINED_FUNCTION_1)
                  000038d0    00000010     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.tramp.__aeabi_dcmpeq.1)
                  000038e0    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000038e8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000038f0    00000008     libc.a : printf.c.obj (.text._outc)
                  000038f8    00000008            : printf.c.obj (.text._outs)
                  00003900    00000008            : fseek.c.obj (.text.fseek)
                  00003908    00000008     libsysbm.a : remove.c.obj (.text.remove)
                  00003910    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003914    00000004     libc.a : exit.c.obj (.text:abort)
                  00003918    00000008     --HOLE-- [fill = 0]

.cinit     0    00003bc0    00000088     
                  00003bc0    00000063     (.cinit..data.load) [load image, compression = lzss]
                  00003c23    00000001     --HOLE-- [fill = 0]
                  00003c24    0000000c     (__TI_handler_table)
                  00003c30    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00003c38    00000010     (__TI_cinit_table)

.rodata    0    00003920    000002a0     
                  00003920    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00003a21    00000003     app_motor_usart.o (.rodata.str1.38835746890475915111)
                  00003a24    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00003a26    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00003a28    00000080     libc.a : strtod.c.obj (.rodata.digits)
                  00003aa8    00000048            : strtod.c.obj (.rodata.powerof10)
                  00003af0    0000001e     empty.o (.rodata.str1.115469792269033204851)
                  00003b0e    0000001a     empty.o (.rodata.str1.100506750686581518081)
                  00003b28    00000016     app_motor_usart.o (.rodata.str1.138129986531886932851)
                  00003b3e    00000012     app_motor_usart.o (.rodata.str1.177430081765570553101)
                  00003b50    00000012     app_motor_usart.o (.rodata.str1.2691616975900509361)
                  00003b62    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00003b73    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00003b84    0000000e     app_motor_usart.o (.rodata.str1.11867396368620600391)
                  00003b92    0000000b     app_motor_usart.o (.rodata.str1.157726972120725782571)
                  00003b9d    0000000b     app_motor_usart.o (.rodata.str1.63670896866782352001)
                  00003ba8    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00003bb2    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00003bbc    00000002     libc.a : fputs.c.obj (.rodata.str1.166077212684151853901)
                  00003bbe    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000800     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000007f0     --HOLE--

.bss       0    20200800    00000430     UNINITIALIZED
                  20200800    00000120     libsysbm.a : trgmsg.c.obj (.bss:_CIOBUF_)
                  20200920    00000100     app_motor_usart.o (.bss.Deal_data_real.data)
                  20200a20    00000100     (.common:g_recv_buff_deal)
                  20200b20    000000a0     libc.a : defs.c.obj (.bss.__TI_tmpnams)
                  20200bc0    00000032     (.common:send_buff)
                  20200bf2    00000001     (.common:g_recv_flag)
                  20200bf3    00000001     --HOLE--
                  20200bf4    00000010     (.common:Encoder_Now)
                  20200c04    00000010     (.common:Encoder_Offset)
                  20200c14    00000010     (.common:g_Speed)
                  20200c24    00000008     libsysbm.a : hostopen.c.obj (.bss.parmbuf)
                  20200c2c    00000004     libc.a : memory.c.obj (.bss.sys_free)

.data      0    20200c30    0000025d     UNINITIALIZED
                  20200c30    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200d20    00000080     usart.o (.data.recv0_buff)
                  20200da0    00000078     libsysbm.a : host_device.c.obj (.data._device)
                  20200e18    00000050                : host_device.c.obj (.data._stream)
                  20200e68    00000008     libc.a : exit.c.obj (.data..L_MergedGlobals)
                  20200e70    00000004            : defs.c.obj (.data.__TI_ft_end)
                  20200e74    00000004            : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200e78    00000004            : _lock.c.obj (.data._lock)
                  20200e7c    00000004            : _lock.c.obj (.data._unlock)
                  20200e80    00000004            : strtok.c.obj (.data.last_end)
                  20200e84    00000004     empty.o (.data.main.control_counter)
                  20200e88    00000002     usart.o (.data.recv0_length)
                  20200e8a    00000001     usart.o (.data.recv0_flag)
                  20200e8b    00000001     --HOLE--
                  20200e8c    00000001     libc.a : memory.c.obj (.data.memory_is_initialized)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             428     24        0      
       empty.o                        232     56        4      
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         666     272       4      
                                                               
    .\BSP\
       app_motor_usart.o              1540    97        611    
       usart.o                        136     0         131    
       delay.o                        64      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1740    97        742    
                                                               
    C:/ti/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_uart.o                      90      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         100     0         0      
                                                               
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       strtod.c.obj                   968     200       0      
       memory.c.obj                   702     0         5      
       defs.c.obj                     0       0         404    
       fputs.c.obj                    284     2         0      
       aeabi_ctype.S.obj              0       257       0      
       setvbuf.c.obj                  224     0         0      
       s_scalbn.c.obj                 216     0         0      
       getdevice.c.obj                160     0         0      
       memcpy16.S.obj                 154     0         0      
       strcmp-armv6m.S.obj            136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       fclose.c.obj                   124     0         0      
       fseek.c.obj                    116     0         0      
       printf.c.obj                   108     0         0      
       _io_perm.c.obj                 100     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       fflush.c.obj                   82      0         0      
       strtok.c.obj                   72      0         4      
       atoi.c.obj                     64      0         0      
       exit.c.obj                     56      0         8      
       autoinit.c.obj                 60      0         0      
       fopen.c.obj                    52      0         0      
       boot_cortex_m.c.obj            40      0         0      
       strspn.c.obj                   38      0         0      
       strcspn.c.obj                  36      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8892    493       433    
                                                               
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       trgmsg.c.obj                   104     0         288    
       host_device.c.obj              0       0         200    
       hostrename.c.obj               136     0         0      
       hostlseek.c.obj                108     0         0      
       hostopen.c.obj                 96      0         8      
       hostread.c.obj                 88      0         0      
       hostwrite.c.obj                88      0         0      
       close.c.obj                    80      0         0      
       hostclose.c.obj                72      0         0      
       hostunlink.c.obj               72      0         0      
       unlink.c.obj                   44      0         0      
       lseek.c.obj                    40      0         0      
       write.c.obj                    40      0         0      
       remove.c.obj                   8       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         976     0         496    
                                                               
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       udivmoddi4.S.obj               162     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               114     0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2040    0         0      
                                                               
       Heap:                          0       0         2048   
       Stack:                         0       0         512    
       Linker Generated:              0       135       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   14414   997       4235   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00003c38 records: 2, size/record: 8, table size: 16
	.data: load addr=00003bc0, load size=00000063 bytes, run addr=20200c30, run size=0000025d bytes, compression=lzss
	.bss: load addr=00003c30, load size=00000008 bytes, run addr=20200800, run size=00000430 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003c24 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000168d     0000389c     0000389a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dcmpeq            $Tramp$TT$L$PI$$__aeabi_dcmpeq
   00002a45     000038d0     000038ce   libc.a : strtod.c.obj (.text.OUTLINED_FUNCTION_1)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000181f  ADC0_IRQHandler               
0000181f  ADC1_IRQHandler               
0000181f  AES_IRQHandler                
00003914  C$$EXIT                       
00003491  C$$IO$$                       
0000181f  CANFD0_IRQHandler             
00002ecd  Contrl_Speed                  
0000181f  DAC0_IRQHandler               
00003889  DL_Common_delayCycles         
00002f65  DL_UART_init                  
000037f1  DL_UART_setClockConfig        
0000181f  DMA_IRQHandler                
00000a91  Deal_data_real                
0000181f  Default_Handler               
20200bf4  Encoder_Now                   
20200c04  Encoder_Offset                
0000181f  GROUP0_IRQHandler             
0000181f  GROUP1_IRQHandler             
00002fad  HOSTclose                     
00002695  HOSTlseek                     
00002b09  HOSTopen                      
00002c21  HOSTread                      
00002701  HOSTrename                    
00002ff5  HOSTunlink                    
00002c79  HOSTwrite                     
0000181f  HardFault_Handler             
0000181f  I2C0_IRQHandler               
0000181f  I2C1_IRQHandler               
0000181f  NMI_Handler                   
0000181f  PendSV_Handler                
0000181f  RTC_IRQHandler                
00003911  Reset_Handler                 
0000181f  SPI0_IRQHandler               
0000181f  SPI1_IRQHandler               
0000181f  SVC_Handler                   
00003799  SYSCFG_DL_GPIO_init           
00003311  SYSCFG_DL_SYSCTL_init         
00003745  SYSCFG_DL_SYSTICK_init        
000025b5  SYSCFG_DL_UART_0_init         
00002625  SYSCFG_DL_UART_1_init         
00003761  SYSCFG_DL_init                
00003111  SYSCFG_DL_initPower           
UNDEFED   Send_Motor_ArrayU8            
0000181f  SysTick_Handler               
0000181f  TIMA0_IRQHandler              
0000181f  TIMA1_IRQHandler              
0000181f  TIMG0_IRQHandler              
0000181f  TIMG12_IRQHandler             
0000181f  TIMG6_IRQHandler              
0000181f  TIMG7_IRQHandler              
0000181f  TIMG8_IRQHandler              
000030cd  UART0_IRQHandler              
0000181f  UART1_IRQHandler              
0000181f  UART2_IRQHandler              
0000181f  UART3_IRQHandler              
00003651  USART_Init                    
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000800  __SYSMEM_SIZE                 
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00003c38  __TI_CINIT_Base               
00003c48  __TI_CINIT_Limit              
00003c48  __TI_CINIT_Warm               
00003c24  __TI_Handler_Table_Base       
00003c30  __TI_Handler_Table_Limit      
0000334d  __TI_auto_init_nobinit_nopinit
000033fd  __TI_cleanup                  
20200e68  __TI_cleanup_ptr              
000023c9  __TI_closefile                
00002445  __TI_decompress_lzss          
00003803  __TI_decompress_none          
00002dd7  __TI_doflush                  
20200e6c  __TI_dtors_ptr                
20200e70  __TI_ft_end                   
00002cd1  __TI_ltoa                     
ffffffff  __TI_pprof_out_hndl           
000000c1  __TI_printfi                  
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00003431  __TI_readmsg                  
00000000  __TI_static_base__            
20200b20  __TI_tmpnams                  
00003465  __TI_writemsg                 
0000297b  __TI_wrt_ok                   
00003825  __TI_zero_init                
00001697  __adddf3                      
00003920  __aeabi_ctype_table_          
00003920  __aeabi_ctype_table_C         
00002541  __aeabi_d2f                   
00002f19  __aeabi_d2iz                  
00001697  __aeabi_dadd                  
00002a45  __aeabi_dcmpeq                
00002a81  __aeabi_dcmpge                
00002a95  __aeabi_dcmpgt                
00002a6d  __aeabi_dcmple                
00002a59  __aeabi_dcmplt                
00001b91  __aeabi_ddiv                  
00001f65  __aeabi_dmul                  
0000168d  __aeabi_dsub                  
20200e74  __aeabi_errno                 
000038e1  __aeabi_errno_addr            
00003191  __aeabi_f2d                   
00003531  __aeabi_i2d                   
00002d81  __aeabi_idiv                  
000029df  __aeabi_idiv0                 
00002d81  __aeabi_idivmod               
00002e7b  __aeabi_ldiv0                 
00003725  __aeabi_llsl                  
00003675  __aeabi_lmul                  
00002535  __aeabi_memclr                
00002535  __aeabi_memclr4               
00002535  __aeabi_memclr8               
000038e9  __aeabi_memcpy                
000038e9  __aeabi_memcpy4               
000038e9  __aeabi_memcpy8               
00003845  __aeabi_memset                
00003845  __aeabi_memset4               
00003845  __aeabi_memset8               
00003151  __aeabi_uidiv                 
00003151  __aeabi_uidivmod              
000037c9  __aeabi_uldivmod              
00003725  __ashldi3                     
ffffffff  __binit__                     
00002845  __cmpdf2                      
00001b91  __divdf3                      
00002845  __eqdf2                       
00003191  __extendsfdf2                 
00002f19  __fixdfsi                     
00003531  __floatsidf                   
000024c1  __gedf2                       
000024c1  __gtdf2                       
00002845  __ledf2                       
00002845  __ltdf2                       
UNDEFED   __mpu_init                    
00001f65  __muldf3                      
00003675  __muldi3                      
00003389  __muldsi3                     
00002845  __nedf2                       
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
0000168d  __subdf3                      
00002541  __truncdfsf2                  
00002201  __udivmoddi4                  
00003601  _c_int00_noargs               
20200da0  _device                       
20200c30  _ftable                       
20200e78  _lock                         
00002f63  _nop                          
20200e18  _stream                       
20200000  _sys_memory                   
UNDEFED   _system_post_cinit            
0000233d  _system_pre_init              
20200e7c  _unlock                       
00003915  abort                         
00001a7d  aligned_alloc                 
000031d1  atoi                          
ffffffff  binit                         
00002e7d  close                         
00003211  delay_ms                      
00003499  exit                          
000034cd  finddevice                    
00003705  fputc                         
00001c9d  fputs                         
00001d95  free                          
00002b69  frexp                         
00002b69  frexpl                        
00003901  fseek                         
0000276d  fseeko                        
20200c14  g_Speed                       
20200a20  g_recv_buff_deal              
20200bf2  g_recv_flag                   
000027d9  getdevice                     
00000000  interruptVectors              
00002129  ldexp                         
00002129  ldexpl                        
000035b1  lseek                         
00001e7d  main                          
0000387d  malloc                        
00001a7d  memalign                      
000036e1  memccpy                       
000022a3  memcpy                        
00002aa7  memset                        
20200c24  parmbuf                       
00002bc5  printf                        
00003699  puts                          
20200d20  recv0_buff                    
20200e8a  recv0_flag                    
20200e88  recv0_length                  
00003909  remove                        
00002129  scalbn                        
00002129  scalbnl                       
20200bc0  send_buff                     
000028ad  send_motor_PID                
00003251  send_motor_deadzone           
00003291  send_motor_type               
000032d1  send_pulse_line               
0000303d  send_upload_data              
00002049  setvbuf                       
000033c5  sprintf                       
00002341  strcmp                        
000036bd  strcspn                       
00003629  strspn                        
00000ed9  strtod                        
00003085  strtok                        
00000ed9  strtold                       
0000355d  unlink                        
00003815  wcslen                        
000035d9  write                         


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  __TI_printfi                  
00000200  __STACK_SIZE                  
00000800  __SYSMEM_SIZE                 
00000a91  Deal_data_real                
00000ed9  strtod                        
00000ed9  strtold                       
0000168d  __aeabi_dsub                  
0000168d  __subdf3                      
00001697  __adddf3                      
00001697  __aeabi_dadd                  
0000181f  ADC0_IRQHandler               
0000181f  ADC1_IRQHandler               
0000181f  AES_IRQHandler                
0000181f  CANFD0_IRQHandler             
0000181f  DAC0_IRQHandler               
0000181f  DMA_IRQHandler                
0000181f  Default_Handler               
0000181f  GROUP0_IRQHandler             
0000181f  GROUP1_IRQHandler             
0000181f  HardFault_Handler             
0000181f  I2C0_IRQHandler               
0000181f  I2C1_IRQHandler               
0000181f  NMI_Handler                   
0000181f  PendSV_Handler                
0000181f  RTC_IRQHandler                
0000181f  SPI0_IRQHandler               
0000181f  SPI1_IRQHandler               
0000181f  SVC_Handler                   
0000181f  SysTick_Handler               
0000181f  TIMA0_IRQHandler              
0000181f  TIMA1_IRQHandler              
0000181f  TIMG0_IRQHandler              
0000181f  TIMG12_IRQHandler             
0000181f  TIMG6_IRQHandler              
0000181f  TIMG7_IRQHandler              
0000181f  TIMG8_IRQHandler              
0000181f  UART1_IRQHandler              
0000181f  UART2_IRQHandler              
0000181f  UART3_IRQHandler              
00001a7d  aligned_alloc                 
00001a7d  memalign                      
00001b91  __aeabi_ddiv                  
00001b91  __divdf3                      
00001c9d  fputs                         
00001d95  free                          
00001e7d  main                          
00001f65  __aeabi_dmul                  
00001f65  __muldf3                      
00002049  setvbuf                       
00002129  ldexp                         
00002129  ldexpl                        
00002129  scalbn                        
00002129  scalbnl                       
00002201  __udivmoddi4                  
000022a3  memcpy                        
0000233d  _system_pre_init              
00002341  strcmp                        
000023c9  __TI_closefile                
00002445  __TI_decompress_lzss          
000024c1  __gedf2                       
000024c1  __gtdf2                       
00002535  __aeabi_memclr                
00002535  __aeabi_memclr4               
00002535  __aeabi_memclr8               
00002541  __aeabi_d2f                   
00002541  __truncdfsf2                  
000025b5  SYSCFG_DL_UART_0_init         
00002625  SYSCFG_DL_UART_1_init         
00002695  HOSTlseek                     
00002701  HOSTrename                    
0000276d  fseeko                        
000027d9  getdevice                     
00002845  __cmpdf2                      
00002845  __eqdf2                       
00002845  __ledf2                       
00002845  __ltdf2                       
00002845  __nedf2                       
000028ad  send_motor_PID                
0000297b  __TI_wrt_ok                   
000029df  __aeabi_idiv0                 
00002a45  __aeabi_dcmpeq                
00002a59  __aeabi_dcmplt                
00002a6d  __aeabi_dcmple                
00002a81  __aeabi_dcmpge                
00002a95  __aeabi_dcmpgt                
00002aa7  memset                        
00002b09  HOSTopen                      
00002b69  frexp                         
00002b69  frexpl                        
00002bc5  printf                        
00002c21  HOSTread                      
00002c79  HOSTwrite                     
00002cd1  __TI_ltoa                     
00002d81  __aeabi_idiv                  
00002d81  __aeabi_idivmod               
00002dd7  __TI_doflush                  
00002e7b  __aeabi_ldiv0                 
00002e7d  close                         
00002ecd  Contrl_Speed                  
00002f19  __aeabi_d2iz                  
00002f19  __fixdfsi                     
00002f63  _nop                          
00002f65  DL_UART_init                  
00002fad  HOSTclose                     
00002ff5  HOSTunlink                    
0000303d  send_upload_data              
00003085  strtok                        
000030cd  UART0_IRQHandler              
00003111  SYSCFG_DL_initPower           
00003151  __aeabi_uidiv                 
00003151  __aeabi_uidivmod              
00003191  __aeabi_f2d                   
00003191  __extendsfdf2                 
000031d1  atoi                          
00003211  delay_ms                      
00003251  send_motor_deadzone           
00003291  send_motor_type               
000032d1  send_pulse_line               
00003311  SYSCFG_DL_SYSCTL_init         
0000334d  __TI_auto_init_nobinit_nopinit
00003389  __muldsi3                     
000033c5  sprintf                       
000033fd  __TI_cleanup                  
00003431  __TI_readmsg                  
00003465  __TI_writemsg                 
00003491  C$$IO$$                       
00003499  exit                          
000034cd  finddevice                    
00003531  __aeabi_i2d                   
00003531  __floatsidf                   
0000355d  unlink                        
000035b1  lseek                         
000035d9  write                         
00003601  _c_int00_noargs               
00003629  strspn                        
00003651  USART_Init                    
00003675  __aeabi_lmul                  
00003675  __muldi3                      
00003699  puts                          
000036bd  strcspn                       
000036e1  memccpy                       
00003705  fputc                         
00003725  __aeabi_llsl                  
00003725  __ashldi3                     
00003745  SYSCFG_DL_SYSTICK_init        
00003761  SYSCFG_DL_init                
00003799  SYSCFG_DL_GPIO_init           
000037c9  __aeabi_uldivmod              
000037f1  DL_UART_setClockConfig        
00003803  __TI_decompress_none          
00003815  wcslen                        
00003825  __TI_zero_init                
00003845  __aeabi_memset                
00003845  __aeabi_memset4               
00003845  __aeabi_memset8               
0000387d  malloc                        
00003889  DL_Common_delayCycles         
000038e1  __aeabi_errno_addr            
000038e9  __aeabi_memcpy                
000038e9  __aeabi_memcpy4               
000038e9  __aeabi_memcpy8               
00003901  fseek                         
00003909  remove                        
00003911  Reset_Handler                 
00003914  C$$EXIT                       
00003915  abort                         
00003920  __aeabi_ctype_table_          
00003920  __aeabi_ctype_table_C         
00003c24  __TI_Handler_Table_Base       
00003c30  __TI_Handler_Table_Limit      
00003c38  __TI_CINIT_Base               
00003c48  __TI_CINIT_Limit              
00003c48  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  _sys_memory                   
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20200a20  g_recv_buff_deal              
20200b20  __TI_tmpnams                  
20200bc0  send_buff                     
20200bf2  g_recv_flag                   
20200bf4  Encoder_Now                   
20200c04  Encoder_Offset                
20200c14  g_Speed                       
20200c24  parmbuf                       
20200c30  _ftable                       
20200d20  recv0_buff                    
20200da0  _device                       
20200e18  _stream                       
20200e68  __TI_cleanup_ptr              
20200e6c  __TI_dtors_ptr                
20200e70  __TI_ft_end                   
20200e74  __aeabi_errno                 
20200e78  _lock                         
20200e7c  _unlock                       
20200e88  recv0_length                  
20200e8a  recv0_flag                    
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   Send_Motor_ArrayU8            
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[221 symbols]
