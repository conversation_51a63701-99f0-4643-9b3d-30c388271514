******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul  2 14:38:13 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000321


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000003e0  0001fc20  R  X
  SRAM                  20200000   00008000  00000486  00007b7a  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000003e0   000003e0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000002f0   000002f0    r-x .text
  000003b0    000003b0    00000030   00000030    r-- .cinit
20200000    20200000    00000287   00000000    rw-
  20200000    20200000    00000201   00000000    rw- .bss
  20200202    20200202    00000085   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000002f0     
                  000000c0    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000015a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000015c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000001d8    00000064     app_motor_usart.o (.text.Deal_Control_Rxtemp)
                  0000023c    00000062     libc.a : memset16.S.obj (.text:memset)
                  0000029e    00000002     empty.o (.text.main)
                  000002a0    00000044     usart.o (.text.UART0_IRQHandler)
                  000002e4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000320    00000028            : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000348    00000020     bsp_motor_usart.o (.text.UART1_IRQHandler)
                  00000368    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000037e    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00000390    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000039c    00000008                            : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000003a4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000003a8    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000003ac    00000004            : exit.c.obj (.text:abort)

.cinit     0    000003b0    00000030     
                  000003b0    0000000c     (__TI_handler_table)
                  000003bc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000003c4    00000008     (.cinit..data.load) [load image, compression = lzss]
                  000003cc    00000010     (__TI_cinit_table)
                  000003dc    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000201     UNINITIALIZED
                  20200000    00000100     (.common:g_recv_buff)
                  20200100    00000100     (.common:g_recv_buff_deal)
                  20200200    00000001     (.common:g_recv_flag)

.data      0    20200202    00000085     UNINITIALIZED
                  20200202    00000080     usart.o (.data.recv0_buff)
                  20200282    00000002     usart.o (.data.recv0_length)
                  20200284    00000001     app_motor_usart.o (.data.Deal_Control_Rxtemp.start_flag)
                  20200285    00000001     app_motor_usart.o (.data.Deal_Control_Rxtemp.step)
                  20200286    00000001     usart.o (.data.recv0_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         8      192       0      
                                                              
    .\BSP\
       app_motor_usart.o              100    0         515    
       usart.o                        68     0         131    
       bsp_motor_usart.o              32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         200    0         646    
                                                              
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       memset16.S.obj                 98     0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         524    0         0      
                                                              
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memset.S.obj             12     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         20     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      44        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   752    236       1158   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000003cc records: 2, size/record: 8, table size: 16
	.bss: load addr=000003bc, load size=00000008 bytes, run addr=20200000, run size=00000201 bytes, compression=zero_init
	.data: load addr=000003c4, load size=00000008 bytes, run addr=20200202, run size=00000085 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000003b0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000015b  ADC0_IRQHandler               
0000015b  ADC1_IRQHandler               
0000015b  AES_IRQHandler                
000003ac  C$$EXIT                       
0000015b  CANFD0_IRQHandler             
0000015b  DAC0_IRQHandler               
0000015b  DMA_IRQHandler                
000001d9  Deal_Control_Rxtemp           
0000015b  Default_Handler               
0000015b  GROUP0_IRQHandler             
0000015b  GROUP1_IRQHandler             
0000015b  HardFault_Handler             
0000015b  I2C0_IRQHandler               
0000015b  I2C1_IRQHandler               
0000015b  NMI_Handler                   
0000015b  PendSV_Handler                
0000015b  RTC_IRQHandler                
000003a5  Reset_Handler                 
0000015b  SPI0_IRQHandler               
0000015b  SPI1_IRQHandler               
0000015b  SVC_Handler                   
0000015b  SysTick_Handler               
0000015b  TIMA0_IRQHandler              
0000015b  TIMA1_IRQHandler              
0000015b  TIMG0_IRQHandler              
0000015b  TIMG12_IRQHandler             
0000015b  TIMG6_IRQHandler              
0000015b  TIMG7_IRQHandler              
0000015b  TIMG8_IRQHandler              
000002a1  UART0_IRQHandler              
00000349  UART1_IRQHandler              
0000015b  UART2_IRQHandler              
0000015b  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000003cc  __TI_CINIT_Base               
000003dc  __TI_CINIT_Limit              
000003dc  __TI_CINIT_Warm               
000003b0  __TI_Handler_Table_Base       
000003bc  __TI_Handler_Table_Limit      
000002e5  __TI_auto_init_nobinit_nopinit
0000015d  __TI_decompress_lzss          
0000037f  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000369  __TI_zero_init_nomemset       
00000391  __aeabi_memclr                
00000391  __aeabi_memclr4               
00000391  __aeabi_memclr8               
0000039d  __aeabi_memcpy                
0000039d  __aeabi_memcpy4               
0000039d  __aeabi_memcpy8               
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000321  _c_int00_noargs               
UNDEFED   _system_post_cinit            
000003a9  _system_pre_init              
000003ad  abort                         
ffffffff  binit                         
20200000  g_recv_buff                   
20200100  g_recv_buff_deal              
20200200  g_recv_flag                   
00000000  interruptVectors              
0000029f  main                          
000000c1  memcpy                        
0000023d  memset                        
20200202  recv0_buff                    
20200286  recv0_flag                    
20200282  recv0_length                  


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  memcpy                        
0000015b  ADC0_IRQHandler               
0000015b  ADC1_IRQHandler               
0000015b  AES_IRQHandler                
0000015b  CANFD0_IRQHandler             
0000015b  DAC0_IRQHandler               
0000015b  DMA_IRQHandler                
0000015b  Default_Handler               
0000015b  GROUP0_IRQHandler             
0000015b  GROUP1_IRQHandler             
0000015b  HardFault_Handler             
0000015b  I2C0_IRQHandler               
0000015b  I2C1_IRQHandler               
0000015b  NMI_Handler                   
0000015b  PendSV_Handler                
0000015b  RTC_IRQHandler                
0000015b  SPI0_IRQHandler               
0000015b  SPI1_IRQHandler               
0000015b  SVC_Handler                   
0000015b  SysTick_Handler               
0000015b  TIMA0_IRQHandler              
0000015b  TIMA1_IRQHandler              
0000015b  TIMG0_IRQHandler              
0000015b  TIMG12_IRQHandler             
0000015b  TIMG6_IRQHandler              
0000015b  TIMG7_IRQHandler              
0000015b  TIMG8_IRQHandler              
0000015b  UART2_IRQHandler              
0000015b  UART3_IRQHandler              
0000015d  __TI_decompress_lzss          
000001d9  Deal_Control_Rxtemp           
00000200  __STACK_SIZE                  
0000023d  memset                        
0000029f  main                          
000002a1  UART0_IRQHandler              
000002e5  __TI_auto_init_nobinit_nopinit
00000321  _c_int00_noargs               
00000349  UART1_IRQHandler              
00000369  __TI_zero_init_nomemset       
0000037f  __TI_decompress_none          
00000391  __aeabi_memclr                
00000391  __aeabi_memclr4               
00000391  __aeabi_memclr8               
0000039d  __aeabi_memcpy                
0000039d  __aeabi_memcpy4               
0000039d  __aeabi_memcpy8               
000003a5  Reset_Handler                 
000003a9  _system_pre_init              
000003ac  C$$EXIT                       
000003ad  abort                         
000003b0  __TI_Handler_Table_Base       
000003bc  __TI_Handler_Table_Limit      
000003cc  __TI_CINIT_Base               
000003dc  __TI_CINIT_Limit              
000003dc  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  g_recv_buff                   
20200100  g_recv_buff_deal              
20200200  g_recv_flag                   
20200202  recv0_buff                    
20200282  recv0_length                  
20200286  recv0_flag                    
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[85 symbols]
