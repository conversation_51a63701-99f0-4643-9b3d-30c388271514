******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul  2 15:22:13 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000224d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00002638  0001d9c8  R  X
  SRAM                  20200000   00008000  000004bc  00007b44  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002478   00002478    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000023b8   000023b8    r-x .text
00002480    00002480    000001c0   000001c0    r--
  00002480    00002480    00000190   00000190    r-- .rodata
  00002610    00002610    00000030   00000030    r-- .cinit
20200000    20200000    000002bd   00000000    rw-
  20200000    20200000    00000233   00000000    rw- .bss
  20200234    20200234    00000089   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000023b8     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000220            : _printfi.c.obj (.text._pconv_a)
                  00000cb0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00000e8c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000101e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001020    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000115c    00000120            : _printfi.c.obj (.text._pconv_e)
                  0000127c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001388    000000e4     empty.o (.text.Send_Motor_ArrayU8)
                  0000146c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001550    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00001628    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000016ca    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00001764    00000098     empty.o (.text.main)
                  000017fc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001878    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000018ec    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  0000195c    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  000019cc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001a34    00000068     app_motor_usart.o (.text.send_motor_PID)
                  00001a9c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00001b02    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001b04    00000064     app_motor_usart.o (.text.Deal_Control_Rxtemp)
                  00001b68    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001bca    00000062     libc.a : memset16.S.obj (.text:memset)
                  00001c2c    0000005c            : s_frexp.c.obj (.text.frexp)
                  00001c88    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  00001ce0    00000058            : _printfi.c.obj (.text._pconv_f)
                  00001d38    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00001d8e    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00001de0    0000004c     app_motor_usart.o (.text.Contrl_Speed)
                  00001e2c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00001e76    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00001e78    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001ec0    00000044     usart.o (.text.UART0_IRQHandler)
                  00001f04    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001f44    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001f84    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001fc4    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00002004    00000040     delay.o (.text.delay_ms)
                  00002044    00000040     app_motor_usart.o (.text.send_motor_deadzone)
                  00002084    00000040     app_motor_usart.o (.text.send_motor_type)
                  000020c4    00000040     app_motor_usart.o (.text.send_pulse_line)
                  00002104    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002140    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000217c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000021b6    00000002     --HOLE-- [fill = 0]
                  000021b8    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  000021f0    00000030            : _printfi.c.obj (.text._fcpy)
                  00002220    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000224c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002274    00000024     usart.o (.text.USART_Init)
                  00002298    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  000022bc    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000022de    00000002     --HOLE-- [fill = 0]
                  000022e0    00000020     bsp_motor_usart.o (.text.UART1_IRQHandler)
                  00002300    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000231e    00000002     --HOLE-- [fill = 0]
                  00002320    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  0000233c    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002358    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00002370    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00002388    00000016            : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  0000239e    00000002     --HOLE-- [fill = 0]
                  000023a0    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000023b4    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000023c8    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000023da    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000023ec    00000010            : wcslen.c.obj (.text.wcslen)
                  000023fc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000240a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00002418    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00002424    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000242e    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00002438    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00002448    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00002452    0000000a            : sprintf.c.obj (.text._outc)
                  0000245c    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00002464    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000246c    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00002470    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002474    00000004            : exit.c.obj (.text:abort)

.cinit     0    00002610    00000030     
                  00002610    0000000c     (__TI_handler_table)
                  0000261c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002624    00000008     (.cinit..data.load) [load image, compression = lzss]
                  0000262c    00000010     (__TI_cinit_table)
                  0000263c    00000004     --HOLE-- [fill = 0]

.rodata    0    00002480    00000190     
                  00002480    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00002581    00000016     app_motor_usart.o (.rodata.str1.138129986531886932851)
                  00002597    00000012     app_motor_usart.o (.rodata.str1.177430081765570553101)
                  000025a9    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  000025ba    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  000025cb    0000000e     app_motor_usart.o (.rodata.str1.11867396368620600391)
                  000025d9    0000000b     app_motor_usart.o (.rodata.str1.157726972120725782571)
                  000025e4    0000000b     app_motor_usart.o (.rodata.str1.63670896866782352001)
                  000025ef    00000001     --HOLE-- [fill = 0]
                  000025f0    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000025fa    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00002604    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00002606    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00002608    00000008     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000233     UNINITIALIZED
                  20200000    00000100     (.common:g_recv_buff)
                  20200100    00000100     (.common:g_recv_buff_deal)
                  20200200    00000032     (.common:send_buff)
                  20200232    00000001     (.common:g_recv_flag)

.data      0    20200234    00000089     UNINITIALIZED
                  20200234    00000080     usart.o (.data.recv0_buff)
                  202002b4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202002b8    00000002     usart.o (.data.recv0_length)
                  202002ba    00000001     app_motor_usart.o (.data.Deal_Control_Rxtemp.start_flag)
                  202002bb    00000001     app_motor_usart.o (.data.Deal_Control_Rxtemp.step)
                  202002bc    00000001     usart.o (.data.recv0_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             428    24        0      
       empty.o                        380    0         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         814    216       0      
                                                              
    .\BSP\
       app_motor_usart.o              472    76        565    
       usart.o                        104    0         131    
       delay.o                        64     0         0      
       bsp_motor_usart.o              32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         672    76        696    
                                                              
    C:/ti/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         100    0         0      
                                                              
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510   34        0      
       aeabi_ctype.S.obj              0      257       0      
       s_scalbn.c.obj                 216    0         0      
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       memset16.S.obj                 98     0         0      
       s_frexp.c.obj                  92     0         0      
       sprintf.c.obj                  90     0         0      
       _ltoa.c.obj                    88     0         0      
       atoi.c.obj                     64     0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       memccpy.c.obj                  34     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       wcslen.c.obj                   16     0         0      
       aeabi_portable.c.obj           8      0         4      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         5642   291       4      
                                                              
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418    0         0      
       divdf3.S.obj                   268    0         0      
       muldf3.S.obj                   228    0         0      
       comparedf2.c.obj               220    0         0      
       udivmoddi4.S.obj               162    0         0      
       aeabi_dcmp.S.obj               98     0         0      
       aeabi_idivmod.S.obj            86     0         0      
       fixdfsi.S.obj                  74     0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       extendsfdf2.S.obj              64     0         0      
       muldsi3.S.obj                  58     0         0      
       floatsidf.S.obj                44     0         0      
       muldi3.S.obj                   36     0         0      
       ashldi3.S.obj                  30     0         0      
       aeabi_memset.S.obj             26     0         0      
       aeabi_uldivmod.S.obj           20     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1908   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      44        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   9136   627       1212   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000262c records: 2, size/record: 8, table size: 16
	.bss: load addr=0000261c, load size=00000008 bytes, run addr=20200000, run size=00000233 bytes, compression=zero_init
	.data: load addr=00002624, load size=00000008 bytes, run addr=20200234, run size=00000089 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002610 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00000e8d     00002438     00002436   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000101f  ADC0_IRQHandler               
0000101f  ADC1_IRQHandler               
0000101f  AES_IRQHandler                
00002474  C$$EXIT                       
0000101f  CANFD0_IRQHandler             
00001de1  Contrl_Speed                  
0000101f  DAC0_IRQHandler               
00002425  DL_Common_delayCycles         
00001e79  DL_UART_init                  
000023c9  DL_UART_setClockConfig        
0000101f  DMA_IRQHandler                
00001b05  Deal_Control_Rxtemp           
0000101f  Default_Handler               
0000101f  GROUP0_IRQHandler             
0000101f  GROUP1_IRQHandler             
0000101f  HardFault_Handler             
0000101f  I2C0_IRQHandler               
0000101f  I2C1_IRQHandler               
0000101f  NMI_Handler                   
0000101f  PendSV_Handler                
0000101f  RTC_IRQHandler                
0000246d  Reset_Handler                 
0000101f  SPI0_IRQHandler               
0000101f  SPI1_IRQHandler               
0000101f  SVC_Handler                   
00002359  SYSCFG_DL_GPIO_init           
00002105  SYSCFG_DL_SYSCTL_init         
00002321  SYSCFG_DL_SYSTICK_init        
000018ed  SYSCFG_DL_UART_0_init         
0000195d  SYSCFG_DL_UART_1_init         
0000233d  SYSCFG_DL_init                
00001f05  SYSCFG_DL_initPower           
00001389  Send_Motor_ArrayU8            
0000101f  SysTick_Handler               
0000101f  TIMA0_IRQHandler              
0000101f  TIMA1_IRQHandler              
0000101f  TIMG0_IRQHandler              
0000101f  TIMG12_IRQHandler             
0000101f  TIMG6_IRQHandler              
0000101f  TIMG7_IRQHandler              
0000101f  TIMG8_IRQHandler              
00001ec1  UART0_IRQHandler              
000022e1  UART1_IRQHandler              
0000101f  UART2_IRQHandler              
0000101f  UART3_IRQHandler              
00002275  USART_Init                    
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
0000262c  __TI_CINIT_Base               
0000263c  __TI_CINIT_Limit              
0000263c  __TI_CINIT_Warm               
00002610  __TI_Handler_Table_Base       
0000261c  __TI_Handler_Table_Limit      
00002141  __TI_auto_init_nobinit_nopinit
000017fd  __TI_decompress_lzss          
000023db  __TI_decompress_none          
00001c89  __TI_ltoa                     
ffffffff  __TI_pprof_out_hndl           
000000c1  __TI_printfi                  
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00002389  __TI_zero_init_nomemset       
00000e97  __adddf3                      
00002480  __aeabi_ctype_table_          
00002480  __aeabi_ctype_table_C         
00001e2d  __aeabi_d2iz                  
00000e97  __aeabi_dadd                  
00001b69  __aeabi_dcmpeq                
00001ba5  __aeabi_dcmpge                
00001bb9  __aeabi_dcmpgt                
00001b91  __aeabi_dcmple                
00001b7d  __aeabi_dcmplt                
0000127d  __aeabi_ddiv                  
0000146d  __aeabi_dmul                  
00000e8d  __aeabi_dsub                  
202002b4  __aeabi_errno                 
0000245d  __aeabi_errno_addr            
00001f85  __aeabi_f2d                   
00002221  __aeabi_i2d                   
00001d39  __aeabi_idiv                  
00001b03  __aeabi_idiv0                 
00001d39  __aeabi_idivmod               
00001e77  __aeabi_ldiv0                 
00002301  __aeabi_llsl                  
00002299  __aeabi_lmul                  
00002419  __aeabi_memclr                
00002419  __aeabi_memclr4               
00002419  __aeabi_memclr8               
00002465  __aeabi_memcpy                
00002465  __aeabi_memcpy4               
00002465  __aeabi_memcpy8               
000023fd  __aeabi_memset                
000023fd  __aeabi_memset4               
000023fd  __aeabi_memset8               
00001f45  __aeabi_uidiv                 
00001f45  __aeabi_uidivmod              
000023a1  __aeabi_uldivmod              
00002301  __ashldi3                     
ffffffff  __binit__                     
000019cd  __cmpdf2                      
0000127d  __divdf3                      
000019cd  __eqdf2                       
00001f85  __extendsfdf2                 
00001e2d  __fixdfsi                     
00002221  __floatsidf                   
00001879  __gedf2                       
00001879  __gtdf2                       
000019cd  __ledf2                       
000019cd  __ltdf2                       
UNDEFED   __mpu_init                    
0000146d  __muldf3                      
00002299  __muldi3                      
0000217d  __muldsi3                     
000019cd  __nedf2                       
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000e8d  __subdf3                      
00001629  __udivmoddi4                  
0000224d  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00002471  _system_pre_init              
00002475  abort                         
00001fc5  atoi                          
ffffffff  binit                         
00002005  delay_ms                      
00001c2d  frexp                         
00001c2d  frexpl                        
20200000  g_recv_buff                   
20200100  g_recv_buff_deal              
20200232  g_recv_flag                   
00000000  interruptVectors              
00001551  ldexp                         
00001551  ldexpl                        
00001765  main                          
000022bd  memccpy                       
000016cb  memcpy                        
00001bcb  memset                        
20200234  recv0_buff                    
202002bc  recv0_flag                    
202002b8  recv0_length                  
00001551  scalbn                        
00001551  scalbnl                       
20200200  send_buff                     
00001a35  send_motor_PID                
00002045  send_motor_deadzone           
00002085  send_motor_type               
000020c5  send_pulse_line               
000021b9  sprintf                       
000023ed  wcslen                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  __TI_printfi                  
00000200  __STACK_SIZE                  
00000e8d  __aeabi_dsub                  
00000e8d  __subdf3                      
00000e97  __adddf3                      
00000e97  __aeabi_dadd                  
0000101f  ADC0_IRQHandler               
0000101f  ADC1_IRQHandler               
0000101f  AES_IRQHandler                
0000101f  CANFD0_IRQHandler             
0000101f  DAC0_IRQHandler               
0000101f  DMA_IRQHandler                
0000101f  Default_Handler               
0000101f  GROUP0_IRQHandler             
0000101f  GROUP1_IRQHandler             
0000101f  HardFault_Handler             
0000101f  I2C0_IRQHandler               
0000101f  I2C1_IRQHandler               
0000101f  NMI_Handler                   
0000101f  PendSV_Handler                
0000101f  RTC_IRQHandler                
0000101f  SPI0_IRQHandler               
0000101f  SPI1_IRQHandler               
0000101f  SVC_Handler                   
0000101f  SysTick_Handler               
0000101f  TIMA0_IRQHandler              
0000101f  TIMA1_IRQHandler              
0000101f  TIMG0_IRQHandler              
0000101f  TIMG12_IRQHandler             
0000101f  TIMG6_IRQHandler              
0000101f  TIMG7_IRQHandler              
0000101f  TIMG8_IRQHandler              
0000101f  UART2_IRQHandler              
0000101f  UART3_IRQHandler              
0000127d  __aeabi_ddiv                  
0000127d  __divdf3                      
00001389  Send_Motor_ArrayU8            
0000146d  __aeabi_dmul                  
0000146d  __muldf3                      
00001551  ldexp                         
00001551  ldexpl                        
00001551  scalbn                        
00001551  scalbnl                       
00001629  __udivmoddi4                  
000016cb  memcpy                        
00001765  main                          
000017fd  __TI_decompress_lzss          
00001879  __gedf2                       
00001879  __gtdf2                       
000018ed  SYSCFG_DL_UART_0_init         
0000195d  SYSCFG_DL_UART_1_init         
000019cd  __cmpdf2                      
000019cd  __eqdf2                       
000019cd  __ledf2                       
000019cd  __ltdf2                       
000019cd  __nedf2                       
00001a35  send_motor_PID                
00001b03  __aeabi_idiv0                 
00001b05  Deal_Control_Rxtemp           
00001b69  __aeabi_dcmpeq                
00001b7d  __aeabi_dcmplt                
00001b91  __aeabi_dcmple                
00001ba5  __aeabi_dcmpge                
00001bb9  __aeabi_dcmpgt                
00001bcb  memset                        
00001c2d  frexp                         
00001c2d  frexpl                        
00001c89  __TI_ltoa                     
00001d39  __aeabi_idiv                  
00001d39  __aeabi_idivmod               
00001de1  Contrl_Speed                  
00001e2d  __aeabi_d2iz                  
00001e2d  __fixdfsi                     
00001e77  __aeabi_ldiv0                 
00001e79  DL_UART_init                  
00001ec1  UART0_IRQHandler              
00001f05  SYSCFG_DL_initPower           
00001f45  __aeabi_uidiv                 
00001f45  __aeabi_uidivmod              
00001f85  __aeabi_f2d                   
00001f85  __extendsfdf2                 
00001fc5  atoi                          
00002005  delay_ms                      
00002045  send_motor_deadzone           
00002085  send_motor_type               
000020c5  send_pulse_line               
00002105  SYSCFG_DL_SYSCTL_init         
00002141  __TI_auto_init_nobinit_nopinit
0000217d  __muldsi3                     
000021b9  sprintf                       
00002221  __aeabi_i2d                   
00002221  __floatsidf                   
0000224d  _c_int00_noargs               
00002275  USART_Init                    
00002299  __aeabi_lmul                  
00002299  __muldi3                      
000022bd  memccpy                       
000022e1  UART1_IRQHandler              
00002301  __aeabi_llsl                  
00002301  __ashldi3                     
00002321  SYSCFG_DL_SYSTICK_init        
0000233d  SYSCFG_DL_init                
00002359  SYSCFG_DL_GPIO_init           
00002389  __TI_zero_init_nomemset       
000023a1  __aeabi_uldivmod              
000023c9  DL_UART_setClockConfig        
000023db  __TI_decompress_none          
000023ed  wcslen                        
000023fd  __aeabi_memset                
000023fd  __aeabi_memset4               
000023fd  __aeabi_memset8               
00002419  __aeabi_memclr                
00002419  __aeabi_memclr4               
00002419  __aeabi_memclr8               
00002425  DL_Common_delayCycles         
0000245d  __aeabi_errno_addr            
00002465  __aeabi_memcpy                
00002465  __aeabi_memcpy4               
00002465  __aeabi_memcpy8               
0000246d  Reset_Handler                 
00002471  _system_pre_init              
00002474  C$$EXIT                       
00002475  abort                         
00002480  __aeabi_ctype_table_          
00002480  __aeabi_ctype_table_C         
00002610  __TI_Handler_Table_Base       
0000261c  __TI_Handler_Table_Limit      
0000262c  __TI_CINIT_Base               
0000263c  __TI_CINIT_Limit              
0000263c  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  g_recv_buff                   
20200100  g_recv_buff_deal              
20200200  send_buff                     
20200232  g_recv_flag                   
20200234  recv0_buff                    
202002b4  __aeabi_errno                 
202002b8  recv0_length                  
202002bc  recv0_flag                    
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[162 symbols]
