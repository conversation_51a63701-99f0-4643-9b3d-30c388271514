******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul  2 16:01:52 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001085


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001300  0001ed00  R  X
  SRAM                  20200000   00008000  0000101f  00006fe1  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001300   00001300    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001170   00001170    r-x .text
  00001230    00001230    00000040   00000040    r-- .rodata
  00001270    00001270    00000090   00000090    r-- .cinit
20200000    20200000    00000e22   00000000    rw-
  20200000    20200000    00000800   00000000    rw- .sysmem
  20200800    20200800    000003cd   00000000    rw- .bss
  20200bd0    20200bd0    00000252   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001170     
                  000000c0    00000114     libc.a : memory.c.obj (.text.aligned_alloc)
                  000001d4    000000f8            : fputs.c.obj (.text.fputs)
                  000002cc    000000e8            : memory.c.obj (.text.free)
                  000003b4    000000e0            : setvbuf.c.obj (.text.setvbuf)
                  00000494    0000009a            : memcpy16.S.obj (.text:memcpy)
                  0000052e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000530    00000088     libc.a : strcmp-armv6m.S.obj (.text:strcmp)
                  000005b8    0000007c            : fclose.c.obj (.text.__TI_closefile)
                  00000634    0000007c            : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000006b0    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00000720    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00000790    0000006c     libsysbm.a : hostlseek.c.obj (.text.HOSTlseek)
                  000007fc    0000006c                : hostrename.c.obj (.text.HOSTrename)
                  00000868    0000006c     libc.a : fseek.c.obj (.text.fseeko)
                  000008d4    0000006c            : getdevice.c.obj (.text.getdevice)
                  00000940    00000064     app_motor_usart.o (.text.Deal_Control_Rxtemp)
                  000009a4    00000064     libc.a : _io_perm.c.obj (.text.__TI_wrt_ok)
                  00000a08    00000064            : memory.c.obj (.text.split)
                  00000a6c    00000062            : memset16.S.obj (.text:memset)
                  00000ace    00000002            : _lock.c.obj (.text._nop)
                  00000ad0    00000060     libsysbm.a : hostopen.c.obj (.text.HOSTopen)
                  00000b30    00000058                : hostread.c.obj (.text.HOSTread)
                  00000b88    00000058                : hostwrite.c.obj (.text.HOSTwrite)
                  00000be0    00000052     libc.a : fflush.c.obj (.text.__TI_doflush)
                  00000c32    00000002     --HOLE-- [fill = 0]
                  00000c34    00000050     libsysbm.a : close.c.obj (.text.close)
                  00000c84    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000ccc    00000048     libsysbm.a : hostclose.c.obj (.text.HOSTclose)
                  00000d14    00000048                : hostunlink.c.obj (.text.HOSTunlink)
                  00000d5c    00000044     usart.o (.text.UART0_IRQHandler)
                  00000da0    00000044     empty.o (.text.main)
                  00000de4    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000e24    00000040     delay.o (.text.delay_ms)
                  00000e64    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000ea0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000edc    00000034            : fopen.c.obj (.text.__TI_cleanup)
                  00000f10    00000034     libsysbm.a : trgmsg.c.obj (.text.__TI_readmsg)
                  00000f44    00000034                : trgmsg.c.obj (.text.__TI_writemsg)
                  00000f78    00000034     libc.a : exit.c.obj (.text.exit)
                  00000fac    00000034            : getdevice.c.obj (.text.finddevice)
                  00000fe0    0000002c     libsysbm.a : unlink.c.obj (.text.unlink)
                  0000100c    00000028     libc.a : memory.c.obj (.text.free_list_insert)
                  00001034    00000028     libsysbm.a : lseek.c.obj (.text.lseek)
                  0000105c    00000028                : write.c.obj (.text.write)
                  00001084    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000010ac    00000024     usart.o (.text.USART_Init)
                  000010d0    00000024     libc.a : fputs.c.obj (.text.puts)
                  000010f4    00000020     bsp_motor_usart.o (.text.UART1_IRQHandler)
                  00001114    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00001130    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000114c    0000001c     libc.a : memory.c.obj (.text.free_list_remove)
                  00001168    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001180    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001196    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000011a8    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000011ba    0000000e            : memory.c.obj (.text.OUTLINED_FUNCTION_0)
                  000011c8    0000000e     libsysbm.a : hostrename.c.obj (.text.strcpy)
                  000011d6    0000000e                : hostrename.c.obj (.text.strlen)
                  000011e4    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000011f0    0000000c     libc.a : memory.c.obj (.text.malloc)
                  000011fc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001206    00000002     --HOLE-- [fill = 0]
                  00001208    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001210    00000008     libc.a : fseek.c.obj (.text.fseek)
                  00001218    00000008     libsysbm.a : remove.c.obj (.text.remove)
                  00001220    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001224    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001228    00000004            : exit.c.obj (.text:abort)
                  0000122c    00000004     --HOLE-- [fill = 0]

.cinit     0    00001270    00000090     
                  00001270    00000065     (.cinit..data.load) [load image, compression = lzss]
                  000012d5    00000003     --HOLE-- [fill = 0]
                  000012d8    0000000c     (__TI_handler_table)
                  000012e4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000012ec    00000010     (__TI_cinit_table)
                  000012fc    00000004     --HOLE-- [fill = 0]

.rodata    0    00001230    00000040     
                  00001230    00000012     empty.o (.rodata.str1.25599521600285148521)
                  00001242    00000010     empty.o (.rodata.str1.115469792269033204851)
                  00001252    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000125c    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00001266    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00001268    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  0000126a    00000002     libc.a : fputs.c.obj (.rodata.str1.166077212684151853901)
                  0000126c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000800     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000007f0     --HOLE--

.bss       0    20200800    000003cd     UNINITIALIZED
                  20200800    00000120     libsysbm.a : trgmsg.c.obj (.bss:_CIOBUF_)
                  20200920    00000100     (.common:g_recv_buff)
                  20200a20    00000100     (.common:g_recv_buff_deal)
                  20200b20    000000a0     libc.a : defs.c.obj (.bss.__TI_tmpnams)
                  20200bc0    00000008     libsysbm.a : hostopen.c.obj (.bss.parmbuf)
                  20200bc8    00000004     libc.a : memory.c.obj (.bss.sys_free)
                  20200bcc    00000001     (.common:g_recv_flag)

.data      0    20200bd0    00000252     UNINITIALIZED
                  20200bd0    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200cc0    00000080     usart.o (.data.recv0_buff)
                  20200d40    00000078     libsysbm.a : host_device.c.obj (.data._device)
                  20200db8    00000050                : host_device.c.obj (.data._stream)
                  20200e08    00000008     libc.a : exit.c.obj (.data..L_MergedGlobals)
                  20200e10    00000004            : defs.c.obj (.data.__TI_ft_end)
                  20200e14    00000004            : _lock.c.obj (.data._lock)
                  20200e18    00000004            : _lock.c.obj (.data._unlock)
                  20200e1c    00000002     usart.o (.data.recv0_length)
                  20200e1e    00000001     app_motor_usart.o (.data.Deal_Control_Rxtemp.start_flag)
                  20200e1f    00000001     app_motor_usart.o (.data.Deal_Control_Rxtemp.step)
                  20200e20    00000001     libc.a : memory.c.obj (.data.memory_is_initialized)
                  20200e21    00000001     usart.o (.data.recv0_flag)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             428    24        0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        68     34        0      
    +--+------------------------------+------+---------+---------+
       Total:                         502    250       0      
                                                              
    .\BSP\
       app_motor_usart.o              100    0         515    
       usart.o                        104    0         131    
       delay.o                        64     0         0      
       bsp_motor_usart.o              32     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         300    0         646    
                                                              
    C:/ti/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         100    0         0      
                                                              
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memory.c.obj                   702    0         5      
       defs.c.obj                     0      0         404    
       fputs.c.obj                    284    2         0      
       setvbuf.c.obj                  224    0         0      
       getdevice.c.obj                160    0         0      
       memcpy16.S.obj                 154    0         0      
       strcmp-armv6m.S.obj            136    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       fclose.c.obj                   124    0         0      
       fseek.c.obj                    116    0         0      
       _io_perm.c.obj                 100    0         0      
       memset16.S.obj                 98     0         0      
       fflush.c.obj                   82     0         0      
       exit.c.obj                     56     0         8      
       autoinit.c.obj                 60     0         0      
       fopen.c.obj                    52     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       _lock.c.obj                    2      0         8      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2558   2         425    
                                                              
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       trgmsg.c.obj                   104    0         288    
       host_device.c.obj              0      0         200    
       hostrename.c.obj               136    0         0      
       hostlseek.c.obj                108    0         0      
       hostopen.c.obj                 96     0         8      
       hostread.c.obj                 88     0         0      
       hostwrite.c.obj                88     0         0      
       close.c.obj                    80     0         0      
       hostclose.c.obj                72     0         0      
       hostunlink.c.obj               72     0         0      
       unlink.c.obj                   44     0         0      
       lseek.c.obj                    40     0         0      
       write.c.obj                    40     0         0      
       remove.c.obj                   8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         976    0         496    
                                                              
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memset.S.obj             12     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         20     0         0      
                                                              
       Heap:                          0      0         2048   
       Stack:                         0      0         512    
       Linker Generated:              0      137       0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   4456   389       4127   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000012ec records: 2, size/record: 8, table size: 16
	.data: load addr=00001270, load size=00000065 bytes, run addr=20200bd0, run size=00000252 bytes, compression=lzss
	.bss: load addr=000012e4, load size=00000008 bytes, run addr=20200800, run size=000003cd bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000012d8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000052f  ADC0_IRQHandler               
0000052f  ADC1_IRQHandler               
0000052f  AES_IRQHandler                
00001228  C$$EXIT                       
00000f71  C$$IO$$                       
0000052f  CANFD0_IRQHandler             
0000052f  DAC0_IRQHandler               
000011fd  DL_Common_delayCycles         
00000c85  DL_UART_init                  
00001197  DL_UART_setClockConfig        
0000052f  DMA_IRQHandler                
00000941  Deal_Control_Rxtemp           
0000052f  Default_Handler               
0000052f  GROUP0_IRQHandler             
0000052f  GROUP1_IRQHandler             
00000ccd  HOSTclose                     
00000791  HOSTlseek                     
00000ad1  HOSTopen                      
00000b31  HOSTread                      
000007fd  HOSTrename                    
00000d15  HOSTunlink                    
00000b89  HOSTwrite                     
0000052f  HardFault_Handler             
0000052f  I2C0_IRQHandler               
0000052f  I2C1_IRQHandler               
0000052f  NMI_Handler                   
0000052f  PendSV_Handler                
0000052f  RTC_IRQHandler                
00001221  Reset_Handler                 
0000052f  SPI0_IRQHandler               
0000052f  SPI1_IRQHandler               
0000052f  SVC_Handler                   
00001169  SYSCFG_DL_GPIO_init           
00000e65  SYSCFG_DL_SYSCTL_init         
00001115  SYSCFG_DL_SYSTICK_init        
000006b1  SYSCFG_DL_UART_0_init         
00000721  SYSCFG_DL_UART_1_init         
00001131  SYSCFG_DL_init                
00000de5  SYSCFG_DL_initPower           
0000052f  SysTick_Handler               
0000052f  TIMA0_IRQHandler              
0000052f  TIMA1_IRQHandler              
0000052f  TIMG0_IRQHandler              
0000052f  TIMG12_IRQHandler             
0000052f  TIMG6_IRQHandler              
0000052f  TIMG7_IRQHandler              
0000052f  TIMG8_IRQHandler              
00000d5d  UART0_IRQHandler              
000010f5  UART1_IRQHandler              
0000052f  UART2_IRQHandler              
0000052f  UART3_IRQHandler              
000010ad  USART_Init                    
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000800  __SYSMEM_SIZE                 
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000012ec  __TI_CINIT_Base               
000012fc  __TI_CINIT_Limit              
000012fc  __TI_CINIT_Warm               
000012d8  __TI_Handler_Table_Base       
000012e4  __TI_Handler_Table_Limit      
00000ea1  __TI_auto_init_nobinit_nopinit
00000edd  __TI_cleanup                  
20200e08  __TI_cleanup_ptr              
000005b9  __TI_closefile                
00000635  __TI_decompress_lzss          
000011a9  __TI_decompress_none          
00000be1  __TI_doflush                  
20200e0c  __TI_dtors_ptr                
20200e10  __TI_ft_end                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000f11  __TI_readmsg                  
00000000  __TI_static_base__            
20200b20  __TI_tmpnams                  
00000f45  __TI_writemsg                 
000009a5  __TI_wrt_ok                   
00001181  __TI_zero_init_nomemset       
000011e5  __aeabi_memclr                
000011e5  __aeabi_memclr4               
000011e5  __aeabi_memclr8               
00001209  __aeabi_memcpy                
00001209  __aeabi_memcpy4               
00001209  __aeabi_memcpy8               
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00001085  _c_int00_noargs               
20200d40  _device                       
20200bd0  _ftable                       
20200e14  _lock                         
00000acf  _nop                          
20200db8  _stream                       
20200000  _sys_memory                   
UNDEFED   _system_post_cinit            
00001225  _system_pre_init              
20200e18  _unlock                       
00001229  abort                         
000000c1  aligned_alloc                 
ffffffff  binit                         
00000c35  close                         
00000e25  delay_ms                      
00000f79  exit                          
00000fad  finddevice                    
000001d5  fputs                         
000002cd  free                          
00001211  fseek                         
00000869  fseeko                        
20200920  g_recv_buff                   
20200a20  g_recv_buff_deal              
20200bcc  g_recv_flag                   
000008d5  getdevice                     
00000000  interruptVectors              
00001035  lseek                         
00000da1  main                          
000011f1  malloc                        
000000c1  memalign                      
00000495  memcpy                        
00000a6d  memset                        
20200bc0  parmbuf                       
000010d1  puts                          
20200cc0  recv0_buff                    
20200e21  recv0_flag                    
20200e1c  recv0_length                  
00001219  remove                        
000003b5  setvbuf                       
00000531  strcmp                        
00000fe1  unlink                        
0000105d  write                         


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  aligned_alloc                 
000000c1  memalign                      
000001d5  fputs                         
00000200  __STACK_SIZE                  
000002cd  free                          
000003b5  setvbuf                       
00000495  memcpy                        
0000052f  ADC0_IRQHandler               
0000052f  ADC1_IRQHandler               
0000052f  AES_IRQHandler                
0000052f  CANFD0_IRQHandler             
0000052f  DAC0_IRQHandler               
0000052f  DMA_IRQHandler                
0000052f  Default_Handler               
0000052f  GROUP0_IRQHandler             
0000052f  GROUP1_IRQHandler             
0000052f  HardFault_Handler             
0000052f  I2C0_IRQHandler               
0000052f  I2C1_IRQHandler               
0000052f  NMI_Handler                   
0000052f  PendSV_Handler                
0000052f  RTC_IRQHandler                
0000052f  SPI0_IRQHandler               
0000052f  SPI1_IRQHandler               
0000052f  SVC_Handler                   
0000052f  SysTick_Handler               
0000052f  TIMA0_IRQHandler              
0000052f  TIMA1_IRQHandler              
0000052f  TIMG0_IRQHandler              
0000052f  TIMG12_IRQHandler             
0000052f  TIMG6_IRQHandler              
0000052f  TIMG7_IRQHandler              
0000052f  TIMG8_IRQHandler              
0000052f  UART2_IRQHandler              
0000052f  UART3_IRQHandler              
00000531  strcmp                        
000005b9  __TI_closefile                
00000635  __TI_decompress_lzss          
000006b1  SYSCFG_DL_UART_0_init         
00000721  SYSCFG_DL_UART_1_init         
00000791  HOSTlseek                     
000007fd  HOSTrename                    
00000800  __SYSMEM_SIZE                 
00000869  fseeko                        
000008d5  getdevice                     
00000941  Deal_Control_Rxtemp           
000009a5  __TI_wrt_ok                   
00000a6d  memset                        
00000acf  _nop                          
00000ad1  HOSTopen                      
00000b31  HOSTread                      
00000b89  HOSTwrite                     
00000be1  __TI_doflush                  
00000c35  close                         
00000c85  DL_UART_init                  
00000ccd  HOSTclose                     
00000d15  HOSTunlink                    
00000d5d  UART0_IRQHandler              
00000da1  main                          
00000de5  SYSCFG_DL_initPower           
00000e25  delay_ms                      
00000e65  SYSCFG_DL_SYSCTL_init         
00000ea1  __TI_auto_init_nobinit_nopinit
00000edd  __TI_cleanup                  
00000f11  __TI_readmsg                  
00000f45  __TI_writemsg                 
00000f71  C$$IO$$                       
00000f79  exit                          
00000fad  finddevice                    
00000fe1  unlink                        
00001035  lseek                         
0000105d  write                         
00001085  _c_int00_noargs               
000010ad  USART_Init                    
000010d1  puts                          
000010f5  UART1_IRQHandler              
00001115  SYSCFG_DL_SYSTICK_init        
00001131  SYSCFG_DL_init                
00001169  SYSCFG_DL_GPIO_init           
00001181  __TI_zero_init_nomemset       
00001197  DL_UART_setClockConfig        
000011a9  __TI_decompress_none          
000011e5  __aeabi_memclr                
000011e5  __aeabi_memclr4               
000011e5  __aeabi_memclr8               
000011f1  malloc                        
000011fd  DL_Common_delayCycles         
00001209  __aeabi_memcpy                
00001209  __aeabi_memcpy4               
00001209  __aeabi_memcpy8               
00001211  fseek                         
00001219  remove                        
00001221  Reset_Handler                 
00001225  _system_pre_init              
00001228  C$$EXIT                       
00001229  abort                         
000012d8  __TI_Handler_Table_Base       
000012e4  __TI_Handler_Table_Limit      
000012ec  __TI_CINIT_Base               
000012fc  __TI_CINIT_Limit              
000012fc  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  _sys_memory                   
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20200920  g_recv_buff                   
20200a20  g_recv_buff_deal              
20200b20  __TI_tmpnams                  
20200bc0  parmbuf                       
20200bcc  g_recv_flag                   
20200bd0  _ftable                       
20200cc0  recv0_buff                    
20200d40  _device                       
20200db8  _stream                       
20200e08  __TI_cleanup_ptr              
20200e0c  __TI_dtors_ptr                
20200e10  __TI_ft_end                   
20200e14  _lock                         
20200e18  _unlock                       
20200e1c  recv0_length                  
20200e21  recv0_flag                    
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[144 symbols]
