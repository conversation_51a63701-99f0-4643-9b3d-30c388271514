/*
 * ========================================================================
 * 文件名称: delay.h
 * 文件描述: 精确延时功能模块头文件
 * 功能说明: 提供基于SysTick定时器的微秒级和毫秒级精确延时功能
 *
 * 实现原理:
 * - 使用ARM Cortex-M0+内核的SysTick系统定时器
 * - 通过读取SysTick->VAL寄存器值计算时间差
 * - 支持微秒(us)和毫秒(ms)级别的精确延时
 *
 * 适用场景:
 * - 需要精确时序控制的场合
 * - 传感器读取时序要求
 * - 通信协议时序控制
 * - 硬件初始化延时等
 *
 * 注意事项:
 * - 延时期间会占用CPU资源(忙等待)
 * - 不适合长时间延时(建议使用定时器中断)
 * - 系统时钟频率变化会影响延时精度
 *
 * 作者: [作者信息]
 * 创建日期: [创建日期]
 * 版本: V1.0
 * ========================================================================
 */

#ifndef _DELAY_H
#define _DELAY_H

// 标准整数类型定义
#include <stdint.h>
// TI官方驱动库配置，包含SysTick相关定义
#include "ti_msp_dl_config.h"

/**
 * @brief 微秒级精确延时函数
 * @param __us 延时时间，单位：微秒(μs)
 * @return void 无返回值
 *
 * @description 基于SysTick定时器实现的微秒级精确延时
 * @note
 * - 使用32MHz系统时钟时，最小延时精度约为0.03125μs
 * - 延时期间CPU处于忙等待状态
 * - 适用于短时间精确延时需求
 *
 * @example
 * delay_us(100);  // 延时100微秒
 */
void delay_us(unsigned long __us);

/**
 * @brief 毫秒级精确延时函数
 * @param ms 延时时间，单位：毫秒(ms)
 * @return void 无返回值
 *
 * @description 基于delay_us函数实现的毫秒级延时
 * @note
 * - 内部调用delay_us(ms * 1000)实现
 * - 继承delay_us的精度特性
 * - 适用于中等时长的延时需求
 *
 * @example
 * delay_ms(50);   // 延时50毫秒
 */
void delay_ms(unsigned long ms);

#endif /* _DELAY_H */
