/*
 * ========================================================================
 * 文件名称: usart.h
 * 文件描述: 串口通信模块头文件
 * 功能说明: 提供UART串口通信的基础功能接口
 *
 * 硬件配置:
 * - UART0: 用于调试输出和printf重定向
 * - UART1: 用于电机控制通信
 *
 * 主要功能:
 * 1. 串口初始化配置
 * 2. 单字节数据发送
 * 3. 串口中断处理
 * 4. printf函数重定向支持
 *
 * 通信参数:
 * - 波特率: 根据SysConfig配置
 * - 数据位: 8位
 * - 停止位: 1位
 * - 校验位: 无
 *
 * 作者: [作者信息]
 * 创建日期: [创建日期]
 * 版本: V1.0
 * ========================================================================
 */

#ifndef __USART_H__
#define __USART_H__

// 包含TI官方驱动库配置头文件
#include "ti_msp_dl_config.h"

/**
 * @brief 串口通信模块初始化函数
 * @param void 无参数
 * @return void 无返回值
 *
 * @description
 * 初始化UART0和UART1串口，配置中断和使能串口功能
 *
 * @note
 * - UART0用于调试输出和printf重定向
 * - UART1用于电机控制板通信
 * - 需要在main函数开始时调用此函数
 */
void USART_Init(void);

/**
 * @brief 串口发送单字节数据函数
 * @param data 要发送的字节数据
 * @return void 无返回值
 *
 * @description
 * 通过UART0发送一个字节的数据，发送前会等待串口空闲
 *
 * @note
 * - 使用忙等待方式，确保数据发送完成
 * - 主要用于调试输出和数据传输
 *
 * @example
 * USART_SendData(0x55);  // 发送字节0x55
 */
void USART_SendData(unsigned char data);

#endif /* __USART_H__ */
