/*
 * ========================================================================
 * 文件名称: delay.c
 * 文件描述: 精确延时功能模块实现文件
 * 功能说明: 基于SysTick定时器的高精度延时函数实现
 *
 * 技术原理:
 * 1. SysTick定时器工作原理:
 *    - SysTick是ARM Cortex-M内核的24位递减计数器
 *    - 每个系统时钟周期递减1
 *    - 计数到0时重新加载LOAD寄存器的值
 *
 * 2. 延时计算方法:
 *    - 系统时钟频率: 32MHz
 *    - 每微秒时钟数: 32000000/1000000 = 32个时钟周期
 *    - 通过监测SysTick->VAL寄存器变化计算经过的时钟周期数
 *
 * 3. 时钟周期计数逻辑:
 *    - 正常递减: tcnt += told - tnow
 *    - 重载情况: tcnt += SysTick->LOAD - tnow + told
 *
 * 作者: [作者信息]
 * 创建日期: [创建日期]
 * 版本: V1.0
 * ========================================================================
 */

#include "delay.h"

// 延时计数器变量(当前未使用，保留用于中断方式延时)
// Delay counter variable (currently unused, reserved for interrupt-based delay)
volatile unsigned int delay_times = 0;

/**
 * @brief 基于SysTick定时器的精确微秒延时函数
 * @param __us 需要延时的微秒数
 * @return void 无返回值
 *
 * @description
 * 通过监测SysTick定时器的VAL寄存器变化来精确计算延时时间
 *
 * @algorithm 延时算法说明:
 * 1. 计算目标时钟周期数 = 延时微秒数 × 32(每微秒的时钟数)
 * 2. 记录起始SysTick值
 * 3. 循环监测SysTick值变化，累计经过的时钟周期
 * 4. 当累计时钟周期达到目标值时退出
 *
 * @note
 * - 系统时钟频率: 32MHz
 * - 理论精度: 1/32MHz ≈ 0.03125μs
 * - tcnt初始值38是为了补偿函数调用开销
 */
// 基于滴答时钟实现的精确us延时
// Accurate us delay with tick timer
void delay_us(unsigned long __us)
{
    uint32_t ticks;     // 目标时钟周期数
    uint32_t told;      // 上一次读取的SysTick值
    uint32_t tnow;      // 当前读取的SysTick值
    uint32_t tcnt = 38; // 已经过的时钟周期计数器(初始值补偿函数开销)

    // 计算需要的时钟数 = 延迟微秒数 * 每微秒时钟数
    // 32MHz系统时钟下，每微秒需要32个时钟周期
    // Calculate the number of clocks required = delay microseconds * number of clocks per microsecond
    ticks = __us * (32000000 / 1000000);

    // 获取当前的SysTick计数值作为起始参考点
    // Get the current SysTick value
    told = SysTick->VAL;

    // 延时主循环 - 监测SysTick变化并累计时钟周期
    while (1)
    {
        // 重复刷新获取当前的SysTick值
        // Repeatedly refresh to get the current SysTick value
        tnow = SysTick->VAL;

        // 检测SysTick值是否发生变化
        if (tnow != told)
        {
            // 情况1: 正常递减 (tnow < told)
            // SysTick从told递减到tnow，经过了(told - tnow)个时钟周期
            if (tnow < told)
                tcnt += told - tnow;
            // 情况2: 计数器重载 (tnow > told)
            // SysTick从told递减到0，然后重载到LOAD值，再递减到tnow
            // 总共经过: (told - 0) + (LOAD - tnow) = LOAD - tnow + told 个时钟周期
            else
                tcnt += SysTick->LOAD - tnow + told;

            // 更新参考值为当前值，准备下次比较
            told = tnow;

            // 检查是否达到目标延时时间
            // 如果达到了需要的时钟数，则退出循环
            // If the required number of clocks is reached, exit the loop
            if (tcnt >= ticks)
                break;
        }
    }
}

/**
 * @brief 毫秒级精确延时函数
 * @param ms 需要延时的毫秒数
 * @return void 无返回值
 *
 * @description
 * 基于delay_us函数实现的毫秒级延时，内部调用delay_us(ms * 1000)
 *
 * @note
 * - 继承delay_us函数的高精度特性
 * - 适用于中等时长的延时需求
 * - 对于长时间延时建议使用定时器中断方式
 *
 * @example
 * delay_ms(100);  // 延时100毫秒
 */
// 基于滴答时钟实现的精确ms延时
// Accurate ms delay with tick timer
void delay_ms(unsigned long ms)
{
    // 调用微秒延时函数，1毫秒 = 1000微秒
    // Call microsecond delay function, 1 millisecond = 1000 microseconds
    delay_us(ms * 1000);
}