/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.21.1+3772"}
 */

/**
 * Import the modules used in this configuration.
 */
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();
const UART2   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

SYSTICK.periodEnable      = true;
SYSTICK.systickEnable     = true;
SYSTICK.interruptPriority = "0";
SYSTICK.period            = 32;

UART1.$name                    = "UART_0";
UART1.uartClkSrc               = "MFCLK";
UART1.enabledInterrupts        = ["RX"];
UART1.targetBaudRate           = 115200;
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                    = "UART_1";
UART2.uartClkSrc               = "MFCLK";
UART2.targetBaudRate           = 115200;
UART2.enabledInterrupts        = ["RX"];
UART2.peripheral.rxPin.$assign = "PB7";
UART2.peripheral.txPin.$assign = "PB6";
UART2.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";
UART2.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
UART1.peripheral.$suggestSolution          = "UART0";
UART2.peripheral.$suggestSolution          = "UART1";
