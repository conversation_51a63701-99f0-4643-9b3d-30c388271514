/*
 * ========================================================================
 * 文件名称: app_motor_usart.h
 * 文件描述: 电机控制串口应用层头文件
 * 功能说明: 定义电机控制通信协议和应用层接口
 *
 * 主要功能:
 * 1. 电机控制通信协议定义
 * 2. 电机参数配置接口
 * 3. 电机控制命令接口
 * 4. 编码器数据处理接口
 * 5. 通信数据解析接口
 *
 * 通信协议:
 * - 使用自定义协议格式: $command:data#
 * - 支持多种电机类型配置
 * - 支持PID参数设置
 * - 支持速度和PWM控制
 * - 支持编码器数据上传
 *
 * 数据流向:
 * - 发送: 主控 -> 电机驱动板 (控制命令、参数配置)
 * - 接收: 电机驱动板 -> 主控 (编码器数据、状态信息)
 *
 * 作者: [作者信息]
 * 创建日期: [创建日期]
 * 版本: V1.0
 * ========================================================================
 */

#ifndef __APP_MOTOR_USART_H_
#define __APP_MOTOR_USART_H_

// 系统头文件包含
#include "ti_msp_dl_config.h" // TI官方驱动库配置
#include "stdio.h"            // 标准输入输出库
#include "bsp_motor_usart.h"  // 电机串口BSP层
#include "string.h"           // 字符串处理库
#include "stdlib.h"           // 标准库函数

// 数据类型简化定义(为了兼容性)
// Simplified data type definitions (for compatibility)
#define u8 uint8_t   // 8位无符号整数
#define u16 uint16_t // 16位无符号整数
#define u32 uint32_t // 32位无符号整数

/*
 * ========================================================================
 * 电机类型枚举定义
 * 功能说明: 定义支持的电机类型，用于配置电机参数和死区判断
 * ========================================================================
 */

/**
 * @brief 电机类型枚举
 * @description
 * 定义系统支持的各种电机类型，用于电机参数配置和控制策略选择
 *
 * @note
 * - 不同电机类型具有不同的控制参数
 * - 死区设置根据电机类型进行调整
 * - 编码器配置因电机类型而异
 */
// 外部声明区   External declaration area
typedef enum _motor_type // 此类型用于判断死区   This type is used to determine the dead zone
{
    MOTOR_TYPE_NONE = 0x00, // 保留类型，未定义电机   Reserved type, undefined motor
    MOTOR_520,              // 520电机(包括L型)   520 motor including L type
    MOTOR_310,              // 310电机   310 motor
    MOTOR_TT_Encoder,       // TT电机(带编码器)   TT motor with encoder
    MOTOR_TT,               // TT电机(不带编码器)   TT motor, without encoder

    Motor_TYPE_MAX // 最后一个电机类型，仅用于边界判断   The last motor type is for boundary judgment only
} motor_type_t;

/*
 * ========================================================================
 * 全局变量外部声明
 * 功能说明: 声明编码器和控制相关的全局变量供外部模块使用
 * ========================================================================
 */

/**
 * @brief 编码器相关全局变量声明
 * @description
 * 这些变量用于存储和处理编码器数据，供外部模块访问
 */
// 导出编码器变量供外部使用   Lead out encoder variables for external use

/**
 * @brief 编码器偏移量数组
 * @description 存储4个电机在10ms时间内的编码器变化量
 * @note 数组索引0-3分别对应电机1-4
 */
extern int Encoder_Offset[4];

/**
 * @brief 编码器当前总计数值数组
 * @description 存储4个电机的累计编码器计数值
 * @note 数组索引0-3分别对应电机1-4
 */
extern int Encoder_Now[4];

/**
 * @brief 电机速度数组
 * @description 存储4个电机的当前速度值
 * @note 数组索引0-3分别对应电机1-4，单位可能是rpm或其他
 */
extern float g_Speed[4];

/**
 * @brief 串口接收完成标志
 * @description 标识是否有新的数据包接收完成
 * @note 1表示有新数据接收完成，需要在处理后清零
 */
extern uint8_t g_recv_flag;

/*
 * ========================================================================
 * 电机控制命令发送函数声明
 * 功能说明: 声明向电机驱动板发送各种配置和控制命令的函数
 * ========================================================================
 */

/**
 * @brief 发送电机类型配置命令
 * @param data 电机类型枚举值
 * @return void 无返回值
 *
 * @description 向电机驱动板发送电机类型配置命令
 * @protocol 协议格式: $mtype:电机类型值#
 */
void send_motor_type(motor_type_t data);

/**
 * @brief 发送电机死区配置命令
 * @param data 死区值
 * @return void 无返回值
 *
 * @description 向电机驱动板发送死区配置命令
 * @protocol 协议格式: $deadzone:死区值#
 */
void send_motor_deadzone(uint16_t data);

/**
 * @brief 发送编码器磁环脉冲数配置命令
 * @param data 每圈脉冲数
 * @return void 无返回值
 *
 * @description 向电机驱动板发送编码器磁环脉冲数配置
 * @protocol 协议格式: $mline:脉冲数#
 */
void send_pulse_line(uint16_t data);

/**
 * @brief 发送电机减速比配置命令
 * @param data 减速比值
 * @return void 无返回值
 *
 * @description 向电机驱动板发送减速比配置命令
 * @protocol 协议格式: $mphase:减速比#
 */
void send_pulse_phase(uint16_t data);

/**
 * @brief 发送车轮直径配置命令
 * @param data 车轮直径值(浮点数)
 * @return void 无返回值
 *
 * @description 向电机驱动板发送车轮直径配置命令
 * @protocol 协议格式: $wdiameter:直径值#
 */
void send_wheel_diameter(float data);

/**
 * @brief 发送PID参数配置命令
 * @param P 比例系数
 * @param I 积分系数
 * @param D 微分系数
 * @return void 无返回值
 *
 * @description 向电机驱动板发送PID控制参数
 * @protocol 协议格式: $mpid:P值,I值,D值#
 */
void send_motor_PID(float P, float I, float D);

/**
 * @brief 发送数据上传配置命令
 * @param ALLEncoder_Switch 全部编码器数据上传开关
 * @param TenEncoder_Switch 10ms编码器数据上传开关
 * @param Speed_Switch 速度数据上传开关
 * @return void 无返回值
 *
 * @description 配置电机驱动板需要上传的数据类型
 * @protocol 协议格式: $upload:开关1,开关2,开关3#
 */
void send_upload_data(bool ALLEncoder_Switch, bool TenEncoder_Switch, bool Speed_Switch);

/**
 * @brief 发送速度控制命令
 * @param M1_speed 电机1目标速度
 * @param M2_speed 电机2目标速度
 * @param M3_speed 电机3目标速度
 * @param M4_speed 电机4目标速度
 * @return void 无返回值
 *
 * @description 向电机驱动板发送4个电机的目标速度
 * @protocol 协议格式: $spd:速度1,速度2,速度3,速度4#
 */
void Contrl_Speed(int16_t M1_speed, int16_t M2_speed, int16_t M3_speed, int16_t M4_speed);

/**
 * @brief 发送PWM控制命令
 * @param M1_pwm 电机1 PWM值
 * @param M2_pwm 电机2 PWM值
 * @param M3_pwm 电机3 PWM值
 * @param M4_pwm 电机4 PWM值
 * @return void 无返回值
 *
 * @description 向电机驱动板发送4个电机的PWM控制值
 * @protocol 协议格式: $pwm:PWM1,PWM2,PWM3,PWM4#
 */
void Contrl_Pwm(int16_t M1_pwm, int16_t M2_pwm, int16_t M3_pwm, int16_t M4_pwm);

/*
 * ========================================================================
 * 数据接收和处理函数声明
 * 功能说明: 声明处理从电机驱动板接收数据的函数
 * ========================================================================
 */

/**
 * @brief 处理接收到的单字节数据
 * @param rxtemp 接收到的字节数据
 * @return void 无返回值
 *
 * @description
 * 处理从电机驱动板接收到的单字节数据，进行协议解析
 * 检查数据包的完整性，提取有效数据
 */
void Deal_Control_Rxtemp(uint8_t rxtemp);

/**
 * @brief 处理接收到的完整数据包
 * @param void 无参数
 * @return void 无返回值
 *
 * @description
 * 处理从电机驱动板接收到的完整数据包
 * 解析协议格式，提取编码器数据和状态信息
 */
void Deal_data_real(void);

#endif
