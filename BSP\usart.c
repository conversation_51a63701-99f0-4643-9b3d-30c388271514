/*
 * ========================================================================
 * 文件名称: usart.c
 * 文件描述: 串口通信模块实现文件
 * 功能说明: 实现UART串口通信的基础功能
 *
 * 主要功能:
 * 1. 串口初始化和配置
 * 2. 数据发送功能
 * 3. 数据接收和缓冲管理
 * 4. 串口中断服务程序
 * 5. printf函数重定向实现
 *
 * 技术特点:
 * - 支持双串口(UART0/UART1)同时工作
 * - 中断方式接收数据，提高系统响应性
 * - 循环缓冲区管理，防止数据丢失
 * - 支持标准C库printf输出重定向
 *
 * 作者: [作者信息]
 * 创建日期: [创建日期]
 * 版本: V1.0
 * ========================================================================
 */

#include "usart.h"
#include "stdio.h"

// UART0接收缓冲区最大长度定义
// Maximum length definition for UART0 receive buffer
#define RE_0_BUFF_LEN_MAX 128

// UART0接收数据缓冲区(使用volatile确保中断安全)
// UART0 receive data buffer (using volatile to ensure interrupt safety)
volatile uint8_t recv0_buff[RE_0_BUFF_LEN_MAX] = {0};

// UART0接收数据长度计数器
// UART0 receive data length counter
volatile uint16_t recv0_length = 0;

// UART0接收完成标志位(1表示有新数据接收完成)
// UART0 receive completion flag (1 indicates new data reception completed)
volatile uint8_t recv0_flag = 0;

/**
 * @brief 串口通信模块初始化函数
 * @param void 无参数
 * @return void 无返回值
 *
 * @description
 * 初始化系统配置和UART0、UART1串口，配置中断并使能串口功能
 *
 * @process 初始化流程:
 * 1. 调用SYSCFG_DL_init()进行系统配置初始化
 * 2. 清除UART0和UART1的中断挂起标志
 * 3. 使能UART0和UART1的中断
 *
 * @note
 * - 必须在使用串口功能前调用此函数
 * - UART0用于调试输出，UART1用于电机控制通信
 * - 串口参数(波特率、数据位等)在SysConfig中配置
 */
void USART_Init(void)
{
	// 系统配置初始化 - 初始化所有外设和时钟配置
	// SYSCFG initialization - Initialize all peripherals and clock configuration
	SYSCFG_DL_init();

	// 清除串口中断挂起标志，确保中断状态干净
	// Clear the serial port interrupt pending flags to ensure clean interrupt state
	NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN); // 清除UART0中断标志
	NVIC_ClearPendingIRQ(UART_1_INST_INT_IRQN); // 清除UART1中断标志

	// 使能串口中断，允许串口产生中断请求
	// Enable serial port interrupts to allow UART to generate interrupt requests
	NVIC_EnableIRQ(UART_0_INST_INT_IRQN); // 使能UART0中断
	NVIC_EnableIRQ(UART_1_INST_INT_IRQN); // 使能UART1中断
}

/**
 * @brief 串口发送单字节数据函数
 * @param data 要发送的字节数据(0x00-0xFF)
 * @return void 无返回值
 *
 * @description
 * 通过UART0发送一个字节的数据，使用忙等待方式确保发送完成
 *
 * @algorithm 发送流程:
 * 1. 检查UART0是否忙碌(正在发送数据)
 * 2. 如果忙碌则等待，直到空闲
 * 3. 调用底层驱动函数发送数据
 *
 * @note
 * - 使用忙等待方式，会阻塞程序执行直到发送完成
 * - 适用于单字节数据的可靠发送
 * - 主要用于调试输出和printf重定向
 *
 * @example
 * USART_SendData('A');     // 发送字符'A'
 * USART_SendData(0x0D);    // 发送回车符
 */
// 串口发送一个字节
// The serial port sends a byte
void USART_SendData(unsigned char data)
{
	// 当串口0忙的时候等待 - 确保上一次发送完成
	// Wait when serial port 0 is busy - ensure previous transmission is complete
	while (DL_UART_isBusy(UART_0_INST) == true)
		;

	// 发送数据到UART0
	// Send data to UART0
	DL_UART_Main_transmitData(UART_0_INST, data);
}

/*
 * ========================================================================
 * printf函数重定向支持部分
 * 功能说明: 将标准C库的printf输出重定向到UART0串口
 * ========================================================================
 */

#if !defined(__MICROLIB)
/**
 * @brief 非微库环境下的支持代码
 * @description
 * 当不使用ARM微库时，需要定义额外的结构体和函数来支持printf重定向
 */
// 不使用微库的话就需要添加下面的函数
// If you don't use the micro library, you need to add the following function

#if (__ARMCLIB_VERSION <= 6000000)
/**
 * @brief ARM编译器AC5版本的文件结构体定义
 * @description
 * 为了兼容AC5编译器，需要定义__FILE结构体
 */
// 如果编译器是AC5  就定义下面这个结构体
// If the compiler is AC5, define the following structure
struct __FILE
{
	int handle; // 文件句柄
};
#endif

// 标准输出文件句柄定义
// Standard output file handle definition
FILE __stdout;

/**
 * @brief 系统退出函数定义
 * @param x 退出码(未使用)
 * @return void 无返回值
 *
 * @description
 * 定义_sys_exit()函数以避免使用半主机模式，防止程序在调试时卡死
 *
 * @note
 * - 半主机模式会导致程序在没有调试器时无法正常运行
 * - 此函数为空实现，避免链接错误
 */
// 定义_sys_exit()以避免使用半主机模式
// Define _sys_exit() to avoid using semihosting mode
void _sys_exit(int x)
{
	x = x; // 避免编译器警告的空操作
}
#endif

/**
 * @brief printf函数重定向实现
 * @param ch 要输出的字符
 * @param stream 文件流指针(未使用)
 * @return int 返回输出的字符
 *
 * @description
 * 重定义fputc函数，将printf的输出重定向到UART0串口
 * 这样就可以使用printf函数通过串口输出调试信息
 *
 * @algorithm 输出流程:
 * 1. 等待UART0空闲(不忙碌)
 * 2. 通过UART0发送字符
 * 3. 返回发送的字符
 *
 * @note
 * - 所有printf、puts等标准输出函数都会调用此函数
 * - 使用忙等待方式确保字符发送完成
 * - 支持格式化输出，如printf("Value: %d\n", value);
 *
 * @example
 * printf("Hello World!\n");        // 输出字符串
 * printf("Value: %d\n", 123);      // 输出格式化数据
 */
// printf函数重定义
// printf function redefinition
int fputc(int ch, FILE *stream)
{
	// 当串口0忙的时候等待，不忙的时候再发送传进来的字符
	// Wait when serial port 0 is busy, and send the incoming characters when it is not busy
	while (DL_UART_isBusy(UART_0_INST) == true)
		;

	// 通过UART0发送字符
	// Send character through UART0
	DL_UART_Main_transmitData(UART_0_INST, ch);

	// 返回发送的字符
	// Return the sent character
	return ch;
}

/*
 * ========================================================================
 * UART0中断服务函数部分
 * 功能说明: 处理UART0的接收中断，实现数据缓冲和标志管理
 * ========================================================================
 */

/**
 * @brief UART0中断服务函数
 * @param void 无参数
 * @return void 无返回值
 *
 * @description
 * 处理UART0的各种中断事件，主要处理接收中断
 * 当有数据通过UART0接收时，此函数会被自动调用
 *
 * @algorithm 中断处理流程:
 * 1. 获取中断类型
 * 2. 如果是接收中断：
 *    - 读取接收到的数据
 *    - 检查缓冲区是否有空间
 *    - 将数据存入缓冲区
 *    - 设置接收完成标志
 * 3. 处理其他类型中断(当前为空)
 *
 * @note
 * - 此函数在中断上下文中执行，应尽量简短
 * - 使用循环缓冲区防止数据丢失
 * - 缓冲区满时会重置长度计数器(简单的溢出处理)
 * - 接收标志需要在主程序中清除
 *
 * @warning
 * - 不要在此函数中执行耗时操作
 * - 不要在此函数中调用可能阻塞的函数
 */
// 串口的中断服务函数
// Serial port interrupt service function
void UART_0_INST_IRQHandler(void)
{
	uint8_t receivedData = 0; // 接收数据临时变量

	// 获取并判断中断类型
	// Get and determine the interrupt type
	switch (DL_UART_getPendingInterrupt(UART_0_INST))
	{
	case DL_UART_IIDX_RX: // 接收中断处理
		// 如果是接收中断	If it is a receive interrupt

		// 从UART0读取接收到的数据
		// Read the received data from UART0
		receivedData = DL_UART_Main_receiveData(UART_0_INST);

		// 检查接收缓冲区是否还有空间
		// Check if there is still space in the receive buffer
		if (recv0_length < RE_0_BUFF_LEN_MAX - 1)
		{
			// 将接收到的数据存入缓冲区，并递增长度计数器
			// Store received data in buffer and increment length counter
			recv0_buff[recv0_length++] = receivedData;
		}
		else
		{
			// 缓冲区满时重置长度计数器(简单的溢出处理)
			// Reset length counter when buffer is full (simple overflow handling)
			recv0_length = 0;
		}

		// 设置接收完成标志，通知主程序有新数据
		// Set receive completion flag to notify main program of new data
		recv0_flag = 1;

		break;

	default: // 其他类型的串口中断处理(当前为空实现)
		// 其他的串口中断	Other serial port interrupts
		break;
	}
}
