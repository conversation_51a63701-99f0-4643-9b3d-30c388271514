******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul  2 15:13:57 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000036e1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00003d28  0001c2d8  R  X
  SRAM                  20200000   00008000  0000108d  00006f73  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003d28   00003d28    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003940   00003940    r-x .text
  00003a00    00003a00    000002a0   000002a0    r-- .rodata
  00003ca0    00003ca0    00000088   00000088    r-- .cinit
20200000    20200000    00000e8d   00000000    rw-
  20200000    20200000    00000800   00000000    rw- .sysmem
  20200800    20200800    00000430   00000000    rw- .bss
  20200c30    20200c30    0000025d   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003940     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000448     app_motor_usart.o (.text.Deal_data_real)
                  00000ed8    000003b8     libc.a : strtod.c.obj (.text.strtod)
                  00001290    00000220            : _printfi.c.obj (.text._pconv_a)
                  000014b0    000001dc            : _printfi.c.obj (.text._pconv_g)
                  0000168c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000181e    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001820    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000195c    00000120            : _printfi.c.obj (.text._pconv_e)
                  00001a7c    00000114            : memory.c.obj (.text.aligned_alloc)
                  00001b90    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001c9c    000000f8     libc.a : fputs.c.obj (.text.fputs)
                  00001d94    000000e8            : memory.c.obj (.text.free)
                  00001e7c    000000e8     empty.o (.text.main)
                  00001f64    000000e4     empty.o (.text.Send_Motor_ArrayU8)
                  00002048    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000212c    000000e0     libc.a : setvbuf.c.obj (.text.setvbuf)
                  0000220c    000000d8            : s_scalbn.c.obj (.text.scalbn)
                  000022e4    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00002386    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00002420    00000088            : strcmp-armv6m.S.obj (.text:strcmp)
                  000024a8    0000007c            : fclose.c.obj (.text.__TI_closefile)
                  00002524    0000007c            : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000025a0    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002614    0000000c                            : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00002620    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002694    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002704    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00002774    0000006c     libsysbm.a : hostlseek.c.obj (.text.HOSTlseek)
                  000027e0    0000006c                : hostrename.c.obj (.text.HOSTrename)
                  0000284c    0000006c     libc.a : fseek.c.obj (.text.fseeko)
                  000028b8    0000006c            : getdevice.c.obj (.text.getdevice)
                  00002924    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000298c    00000068     app_motor_usart.o (.text.send_motor_PID)
                  000029f4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002a5a    00000064            : _io_perm.c.obj (.text.__TI_wrt_ok)
                  00002abe    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002ac0    00000064     libc.a : memory.c.obj (.text.split)
                  00002b24    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002b86    00000062     libc.a : memset16.S.obj (.text:memset)
                  00002be8    00000060     libsysbm.a : hostopen.c.obj (.text.HOSTopen)
                  00002c48    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00002ca4    0000005c            : printf.c.obj (.text.printf)
                  00002d00    00000058     libsysbm.a : hostread.c.obj (.text.HOSTread)
                  00002d58    00000058                : hostwrite.c.obj (.text.HOSTwrite)
                  00002db0    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00002e08    00000058            : _printfi.c.obj (.text._pconv_f)
                  00002e60    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00002eb6    00000052     libc.a : fflush.c.obj (.text.__TI_doflush)
                  00002f08    00000052            : _printfi.c.obj (.text._ecpy)
                  00002f5a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002f5c    00000050     libsysbm.a : close.c.obj (.text.close)
                  00002fac    0000004c     app_motor_usart.o (.text.Contrl_Speed)
                  00002ff8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003042    00000002     libc.a : _lock.c.obj (.text._nop)
                  00003044    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000308c    00000048     libsysbm.a : hostclose.c.obj (.text.HOSTclose)
                  000030d4    00000048                : hostunlink.c.obj (.text.HOSTunlink)
                  0000311c    00000048     app_motor_usart.o (.text.send_upload_data)
                  00003164    00000048     libc.a : strtok.c.obj (.text.strtok)
                  000031ac    00000044     usart.o (.text.UART0_IRQHandler)
                  000031f0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003230    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003270    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000032b0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000032f0    00000040     delay.o (.text.delay_ms)
                  00003330    00000040     app_motor_usart.o (.text.send_motor_deadzone)
                  00003370    00000040     app_motor_usart.o (.text.send_motor_type)
                  000033b0    00000040     app_motor_usart.o (.text.send_pulse_line)
                  000033f0    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000342c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003468    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000034a2    00000002     --HOLE-- [fill = 0]
                  000034a4    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  000034dc    00000034            : fopen.c.obj (.text.__TI_cleanup)
                  00003510    00000034     libsysbm.a : trgmsg.c.obj (.text.__TI_readmsg)
                  00003544    00000034                : trgmsg.c.obj (.text.__TI_writemsg)
                  00003578    00000034     libc.a : exit.c.obj (.text.exit)
                  000035ac    00000034            : getdevice.c.obj (.text.finddevice)
                  000035e0    00000030            : _printfi.c.obj (.text._fcpy)
                  00003610    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000363c    0000002c     libsysbm.a : unlink.c.obj (.text.unlink)
                  00003668    00000028     libc.a : memory.c.obj (.text.free_list_insert)
                  00003690    00000028     libsysbm.a : lseek.c.obj (.text.lseek)
                  000036b8    00000028                : write.c.obj (.text.write)
                  000036e0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003708    00000026            : strspn.c.obj (.text.strspn)
                  0000372e    00000002     --HOLE-- [fill = 0]
                  00003730    00000024     usart.o (.text.USART_Init)
                  00003754    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00003778    00000024     libc.a : fputs.c.obj (.text.puts)
                  0000379c    00000024            : strcspn.c.obj (.text.strcspn)
                  000037c0    00000022            : memccpy.c.obj (.text.memccpy)
                  000037e2    00000002     --HOLE-- [fill = 0]
                  000037e4    00000020     usart.o (.text.fputc)
                  00003804    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003822    00000002     --HOLE-- [fill = 0]
                  00003824    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00003840    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000385c    0000001c     libc.a : memory.c.obj (.text.free_list_remove)
                  00003878    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00003890    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000038a8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000038bc    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000038d0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000038e2    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000038f4    00000010            : wcslen.c.obj (.text.wcslen)
                  00003904    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00003914    0000000e            : memory.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003922    00000002     --HOLE-- [fill = 0]
                  00003924    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00003932    0000000e     libsysbm.a : hostrename.c.obj (.text.strcpy)
                  00003940    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  0000394e    0000000e     libsysbm.a : hostrename.c.obj (.text.strlen)
                  0000395c    0000000c     libc.a : memory.c.obj (.text.malloc)
                  00003968    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003972    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000397c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  0000398c    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00003996    0000000a            : sprintf.c.obj (.text._outc)
                  000039a0    00000008            : strtod.c.obj (.text.OUTLINED_FUNCTION_0)
                  000039a8    00000008            : strtod.c.obj (.text.OUTLINED_FUNCTION_1)
                  000039b0    00000010     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.tramp.__aeabi_dcmpeq.1)
                  000039c0    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000039c8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000039d0    00000008     libc.a : printf.c.obj (.text._outc)
                  000039d8    00000008            : printf.c.obj (.text._outs)
                  000039e0    00000008            : fseek.c.obj (.text.fseek)
                  000039e8    00000008     libsysbm.a : remove.c.obj (.text.remove)
                  000039f0    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000039f4    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000039f8    00000004            : exit.c.obj (.text:abort)
                  000039fc    00000004     --HOLE-- [fill = 0]

.cinit     0    00003ca0    00000088     
                  00003ca0    00000062     (.cinit..data.load) [load image, compression = lzss]
                  00003d02    00000002     --HOLE-- [fill = 0]
                  00003d04    0000000c     (__TI_handler_table)
                  00003d10    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00003d18    00000010     (__TI_cinit_table)

.rodata    0    00003a00    000002a0     
                  00003a00    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00003b01    00000003     app_motor_usart.o (.rodata.str1.38835746890475915111)
                  00003b04    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00003b06    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00003b08    00000080     libc.a : strtod.c.obj (.rodata.digits)
                  00003b88    00000048            : strtod.c.obj (.rodata.powerof10)
                  00003bd0    0000001e     empty.o (.rodata.str1.115469792269033204851)
                  00003bee    0000001a     empty.o (.rodata.str1.100506750686581518081)
                  00003c08    00000016     app_motor_usart.o (.rodata.str1.138129986531886932851)
                  00003c1e    00000012     app_motor_usart.o (.rodata.str1.177430081765570553101)
                  00003c30    00000012     app_motor_usart.o (.rodata.str1.2691616975900509361)
                  00003c42    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00003c53    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00003c64    0000000e     app_motor_usart.o (.rodata.str1.11867396368620600391)
                  00003c72    0000000b     app_motor_usart.o (.rodata.str1.157726972120725782571)
                  00003c7d    0000000b     app_motor_usart.o (.rodata.str1.63670896866782352001)
                  00003c88    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00003c92    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00003c9c    00000002     libc.a : fputs.c.obj (.rodata.str1.166077212684151853901)
                  00003c9e    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.sysmem    0    20200000    00000800     UNINITIALIZED
                  20200000    00000010     libc.a : memory.c.obj (.sysmem)
                  20200010    000007f0     --HOLE--

.bss       0    20200800    00000430     UNINITIALIZED
                  20200800    00000120     libsysbm.a : trgmsg.c.obj (.bss:_CIOBUF_)
                  20200920    00000100     app_motor_usart.o (.bss.Deal_data_real.data)
                  20200a20    00000100     (.common:g_recv_buff_deal)
                  20200b20    000000a0     libc.a : defs.c.obj (.bss.__TI_tmpnams)
                  20200bc0    00000032     (.common:send_buff)
                  20200bf2    00000001     (.common:g_recv_flag)
                  20200bf3    00000001     --HOLE--
                  20200bf4    00000010     (.common:Encoder_Now)
                  20200c04    00000010     (.common:Encoder_Offset)
                  20200c14    00000010     (.common:g_Speed)
                  20200c24    00000008     libsysbm.a : hostopen.c.obj (.bss.parmbuf)
                  20200c2c    00000004     libc.a : memory.c.obj (.bss.sys_free)

.data      0    20200c30    0000025d     UNINITIALIZED
                  20200c30    000000f0     libc.a : defs.c.obj (.data._ftable)
                  20200d20    00000080     usart.o (.data.recv0_buff)
                  20200da0    00000078     libsysbm.a : host_device.c.obj (.data._device)
                  20200e18    00000050                : host_device.c.obj (.data._stream)
                  20200e68    00000008     libc.a : exit.c.obj (.data..L_MergedGlobals)
                  20200e70    00000004            : defs.c.obj (.data.__TI_ft_end)
                  20200e74    00000004            : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200e78    00000004            : _lock.c.obj (.data._lock)
                  20200e7c    00000004            : _lock.c.obj (.data._unlock)
                  20200e80    00000004            : strtok.c.obj (.data.last_end)
                  20200e84    00000004     empty.o (.data.main.control_counter)
                  20200e88    00000002     usart.o (.data.recv0_length)
                  20200e8a    00000001     usart.o (.data.recv0_flag)
                  20200e8b    00000001     --HOLE--
                  20200e8c    00000001     libc.a : memory.c.obj (.data.memory_is_initialized)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       empty.o                        460     56        4      
       ti_msp_dl_config.o             428     24        0      
       startup_mspm0g350x_ticlang.o   6       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         894     272       4      
                                                               
    .\BSP\
       app_motor_usart.o              1540    97        611    
       usart.o                        136     0         131    
       delay.o                        64      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1740    97        742    
                                                               
    C:/ti/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_uart.o                      90      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         100     0         0      
                                                               
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       strtod.c.obj                   968     200       0      
       memory.c.obj                   702     0         5      
       defs.c.obj                     0       0         404    
       fputs.c.obj                    284     2         0      
       aeabi_ctype.S.obj              0       257       0      
       setvbuf.c.obj                  224     0         0      
       s_scalbn.c.obj                 216     0         0      
       getdevice.c.obj                160     0         0      
       memcpy16.S.obj                 154     0         0      
       strcmp-armv6m.S.obj            136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       fclose.c.obj                   124     0         0      
       fseek.c.obj                    116     0         0      
       printf.c.obj                   108     0         0      
       _io_perm.c.obj                 100     0         0      
       memset16.S.obj                 98      0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       fflush.c.obj                   82      0         0      
       strtok.c.obj                   72      0         4      
       atoi.c.obj                     64      0         0      
       exit.c.obj                     56      0         8      
       autoinit.c.obj                 60      0         0      
       fopen.c.obj                    52      0         0      
       boot_cortex_m.c.obj            40      0         0      
       strspn.c.obj                   38      0         0      
       strcspn.c.obj                  36      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       aeabi_portable.c.obj           8       0         4      
       _lock.c.obj                    2       0         8      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8892    493       433    
                                                               
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       trgmsg.c.obj                   104     0         288    
       host_device.c.obj              0       0         200    
       hostrename.c.obj               136     0         0      
       hostlseek.c.obj                108     0         0      
       hostopen.c.obj                 96      0         8      
       hostread.c.obj                 88      0         0      
       hostwrite.c.obj                88      0         0      
       close.c.obj                    80      0         0      
       hostclose.c.obj                72      0         0      
       hostunlink.c.obj               72      0         0      
       unlink.c.obj                   44      0         0      
       lseek.c.obj                    40      0         0      
       write.c.obj                    40      0         0      
       remove.c.obj                   8       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         976     0         496    
                                                               
    E:\CCSTheia\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       udivmoddi4.S.obj               162     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               114     0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2040    0         0      
                                                               
       Heap:                          0       0         2048   
       Stack:                         0       0         512    
       Linker Generated:              0       134       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   14642   996       4235   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00003d18 records: 2, size/record: 8, table size: 16
	.data: load addr=00003ca0, load size=00000062 bytes, run addr=20200c30, run size=0000025d bytes, compression=lzss
	.bss: load addr=00003d10, load size=00000008 bytes, run addr=20200800, run size=00000430 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003d04 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000168d     0000397c     0000397a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dcmpeq            $Tramp$TT$L$PI$$__aeabi_dcmpeq
   00002b25     000039b0     000039ae   libc.a : strtod.c.obj (.text.OUTLINED_FUNCTION_1)

[2 trampolines]
[2 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
0000181f  ADC0_IRQHandler               
0000181f  ADC1_IRQHandler               
0000181f  AES_IRQHandler                
000039f8  C$$EXIT                       
00003571  C$$IO$$                       
0000181f  CANFD0_IRQHandler             
00002fad  Contrl_Speed                  
0000181f  DAC0_IRQHandler               
00003969  DL_Common_delayCycles         
00003045  DL_UART_init                  
000038d1  DL_UART_setClockConfig        
0000181f  DMA_IRQHandler                
00000a91  Deal_data_real                
0000181f  Default_Handler               
20200bf4  Encoder_Now                   
20200c04  Encoder_Offset                
0000181f  GROUP0_IRQHandler             
0000181f  GROUP1_IRQHandler             
0000308d  HOSTclose                     
00002775  HOSTlseek                     
00002be9  HOSTopen                      
00002d01  HOSTread                      
000027e1  HOSTrename                    
000030d5  HOSTunlink                    
00002d59  HOSTwrite                     
0000181f  HardFault_Handler             
0000181f  I2C0_IRQHandler               
0000181f  I2C1_IRQHandler               
0000181f  NMI_Handler                   
0000181f  PendSV_Handler                
0000181f  RTC_IRQHandler                
000039f1  Reset_Handler                 
0000181f  SPI0_IRQHandler               
0000181f  SPI1_IRQHandler               
0000181f  SVC_Handler                   
00003879  SYSCFG_DL_GPIO_init           
000033f1  SYSCFG_DL_SYSCTL_init         
00003825  SYSCFG_DL_SYSTICK_init        
00002695  SYSCFG_DL_UART_0_init         
00002705  SYSCFG_DL_UART_1_init         
00003841  SYSCFG_DL_init                
000031f1  SYSCFG_DL_initPower           
00001f65  Send_Motor_ArrayU8            
0000181f  SysTick_Handler               
0000181f  TIMA0_IRQHandler              
0000181f  TIMA1_IRQHandler              
0000181f  TIMG0_IRQHandler              
0000181f  TIMG12_IRQHandler             
0000181f  TIMG6_IRQHandler              
0000181f  TIMG7_IRQHandler              
0000181f  TIMG8_IRQHandler              
000031ad  UART0_IRQHandler              
0000181f  UART1_IRQHandler              
0000181f  UART2_IRQHandler              
0000181f  UART3_IRQHandler              
00003731  USART_Init                    
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000800  __SYSMEM_SIZE                 
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00003d18  __TI_CINIT_Base               
00003d28  __TI_CINIT_Limit              
00003d28  __TI_CINIT_Warm               
00003d04  __TI_Handler_Table_Base       
00003d10  __TI_Handler_Table_Limit      
0000342d  __TI_auto_init_nobinit_nopinit
000034dd  __TI_cleanup                  
20200e68  __TI_cleanup_ptr              
000024a9  __TI_closefile                
00002525  __TI_decompress_lzss          
000038e3  __TI_decompress_none          
00002eb7  __TI_doflush                  
20200e6c  __TI_dtors_ptr                
20200e70  __TI_ft_end                   
00002db1  __TI_ltoa                     
ffffffff  __TI_pprof_out_hndl           
000000c1  __TI_printfi                  
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00003511  __TI_readmsg                  
00000000  __TI_static_base__            
20200b20  __TI_tmpnams                  
00003545  __TI_writemsg                 
00002a5b  __TI_wrt_ok                   
00003905  __TI_zero_init                
00001697  __adddf3                      
00003a00  __aeabi_ctype_table_          
00003a00  __aeabi_ctype_table_C         
00002621  __aeabi_d2f                   
00002ff9  __aeabi_d2iz                  
00001697  __aeabi_dadd                  
00002b25  __aeabi_dcmpeq                
00002b61  __aeabi_dcmpge                
00002b75  __aeabi_dcmpgt                
00002b4d  __aeabi_dcmple                
00002b39  __aeabi_dcmplt                
00001b91  __aeabi_ddiv                  
00002049  __aeabi_dmul                  
0000168d  __aeabi_dsub                  
20200e74  __aeabi_errno                 
000039c1  __aeabi_errno_addr            
00003271  __aeabi_f2d                   
00003611  __aeabi_i2d                   
00002e61  __aeabi_idiv                  
00002abf  __aeabi_idiv0                 
00002e61  __aeabi_idivmod               
00002f5b  __aeabi_ldiv0                 
00003805  __aeabi_llsl                  
00003755  __aeabi_lmul                  
00002615  __aeabi_memclr                
00002615  __aeabi_memclr4               
00002615  __aeabi_memclr8               
000039c9  __aeabi_memcpy                
000039c9  __aeabi_memcpy4               
000039c9  __aeabi_memcpy8               
00003925  __aeabi_memset                
00003925  __aeabi_memset4               
00003925  __aeabi_memset8               
00003231  __aeabi_uidiv                 
00003231  __aeabi_uidivmod              
000038a9  __aeabi_uldivmod              
00003805  __ashldi3                     
ffffffff  __binit__                     
00002925  __cmpdf2                      
00001b91  __divdf3                      
00002925  __eqdf2                       
00003271  __extendsfdf2                 
00002ff9  __fixdfsi                     
00003611  __floatsidf                   
000025a1  __gedf2                       
000025a1  __gtdf2                       
00002925  __ledf2                       
00002925  __ltdf2                       
UNDEFED   __mpu_init                    
00002049  __muldf3                      
00003755  __muldi3                      
00003469  __muldsi3                     
00002925  __nedf2                       
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
0000168d  __subdf3                      
00002621  __truncdfsf2                  
000022e5  __udivmoddi4                  
000036e1  _c_int00_noargs               
20200da0  _device                       
20200c30  _ftable                       
20200e78  _lock                         
00003043  _nop                          
20200e18  _stream                       
20200000  _sys_memory                   
UNDEFED   _system_post_cinit            
000039f5  _system_pre_init              
20200e7c  _unlock                       
000039f9  abort                         
00001a7d  aligned_alloc                 
000032b1  atoi                          
ffffffff  binit                         
00002f5d  close                         
000032f1  delay_ms                      
00003579  exit                          
000035ad  finddevice                    
000037e5  fputc                         
00001c9d  fputs                         
00001d95  free                          
00002c49  frexp                         
00002c49  frexpl                        
000039e1  fseek                         
0000284d  fseeko                        
20200c14  g_Speed                       
20200a20  g_recv_buff_deal              
20200bf2  g_recv_flag                   
000028b9  getdevice                     
00000000  interruptVectors              
0000220d  ldexp                         
0000220d  ldexpl                        
00003691  lseek                         
00001e7d  main                          
0000395d  malloc                        
00001a7d  memalign                      
000037c1  memccpy                       
00002387  memcpy                        
00002b87  memset                        
20200c24  parmbuf                       
00002ca5  printf                        
00003779  puts                          
20200d20  recv0_buff                    
20200e8a  recv0_flag                    
20200e88  recv0_length                  
000039e9  remove                        
0000220d  scalbn                        
0000220d  scalbnl                       
20200bc0  send_buff                     
0000298d  send_motor_PID                
00003331  send_motor_deadzone           
00003371  send_motor_type               
000033b1  send_pulse_line               
0000311d  send_upload_data              
0000212d  setvbuf                       
000034a5  sprintf                       
00002421  strcmp                        
0000379d  strcspn                       
00003709  strspn                        
00000ed9  strtod                        
00003165  strtok                        
00000ed9  strtold                       
0000363d  unlink                        
000038f5  wcslen                        
000036b9  write                         


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  __TI_printfi                  
00000200  __STACK_SIZE                  
00000800  __SYSMEM_SIZE                 
00000a91  Deal_data_real                
00000ed9  strtod                        
00000ed9  strtold                       
0000168d  __aeabi_dsub                  
0000168d  __subdf3                      
00001697  __adddf3                      
00001697  __aeabi_dadd                  
0000181f  ADC0_IRQHandler               
0000181f  ADC1_IRQHandler               
0000181f  AES_IRQHandler                
0000181f  CANFD0_IRQHandler             
0000181f  DAC0_IRQHandler               
0000181f  DMA_IRQHandler                
0000181f  Default_Handler               
0000181f  GROUP0_IRQHandler             
0000181f  GROUP1_IRQHandler             
0000181f  HardFault_Handler             
0000181f  I2C0_IRQHandler               
0000181f  I2C1_IRQHandler               
0000181f  NMI_Handler                   
0000181f  PendSV_Handler                
0000181f  RTC_IRQHandler                
0000181f  SPI0_IRQHandler               
0000181f  SPI1_IRQHandler               
0000181f  SVC_Handler                   
0000181f  SysTick_Handler               
0000181f  TIMA0_IRQHandler              
0000181f  TIMA1_IRQHandler              
0000181f  TIMG0_IRQHandler              
0000181f  TIMG12_IRQHandler             
0000181f  TIMG6_IRQHandler              
0000181f  TIMG7_IRQHandler              
0000181f  TIMG8_IRQHandler              
0000181f  UART1_IRQHandler              
0000181f  UART2_IRQHandler              
0000181f  UART3_IRQHandler              
00001a7d  aligned_alloc                 
00001a7d  memalign                      
00001b91  __aeabi_ddiv                  
00001b91  __divdf3                      
00001c9d  fputs                         
00001d95  free                          
00001e7d  main                          
00001f65  Send_Motor_ArrayU8            
00002049  __aeabi_dmul                  
00002049  __muldf3                      
0000212d  setvbuf                       
0000220d  ldexp                         
0000220d  ldexpl                        
0000220d  scalbn                        
0000220d  scalbnl                       
000022e5  __udivmoddi4                  
00002387  memcpy                        
00002421  strcmp                        
000024a9  __TI_closefile                
00002525  __TI_decompress_lzss          
000025a1  __gedf2                       
000025a1  __gtdf2                       
00002615  __aeabi_memclr                
00002615  __aeabi_memclr4               
00002615  __aeabi_memclr8               
00002621  __aeabi_d2f                   
00002621  __truncdfsf2                  
00002695  SYSCFG_DL_UART_0_init         
00002705  SYSCFG_DL_UART_1_init         
00002775  HOSTlseek                     
000027e1  HOSTrename                    
0000284d  fseeko                        
000028b9  getdevice                     
00002925  __cmpdf2                      
00002925  __eqdf2                       
00002925  __ledf2                       
00002925  __ltdf2                       
00002925  __nedf2                       
0000298d  send_motor_PID                
00002a5b  __TI_wrt_ok                   
00002abf  __aeabi_idiv0                 
00002b25  __aeabi_dcmpeq                
00002b39  __aeabi_dcmplt                
00002b4d  __aeabi_dcmple                
00002b61  __aeabi_dcmpge                
00002b75  __aeabi_dcmpgt                
00002b87  memset                        
00002be9  HOSTopen                      
00002c49  frexp                         
00002c49  frexpl                        
00002ca5  printf                        
00002d01  HOSTread                      
00002d59  HOSTwrite                     
00002db1  __TI_ltoa                     
00002e61  __aeabi_idiv                  
00002e61  __aeabi_idivmod               
00002eb7  __TI_doflush                  
00002f5b  __aeabi_ldiv0                 
00002f5d  close                         
00002fad  Contrl_Speed                  
00002ff9  __aeabi_d2iz                  
00002ff9  __fixdfsi                     
00003043  _nop                          
00003045  DL_UART_init                  
0000308d  HOSTclose                     
000030d5  HOSTunlink                    
0000311d  send_upload_data              
00003165  strtok                        
000031ad  UART0_IRQHandler              
000031f1  SYSCFG_DL_initPower           
00003231  __aeabi_uidiv                 
00003231  __aeabi_uidivmod              
00003271  __aeabi_f2d                   
00003271  __extendsfdf2                 
000032b1  atoi                          
000032f1  delay_ms                      
00003331  send_motor_deadzone           
00003371  send_motor_type               
000033b1  send_pulse_line               
000033f1  SYSCFG_DL_SYSCTL_init         
0000342d  __TI_auto_init_nobinit_nopinit
00003469  __muldsi3                     
000034a5  sprintf                       
000034dd  __TI_cleanup                  
00003511  __TI_readmsg                  
00003545  __TI_writemsg                 
00003571  C$$IO$$                       
00003579  exit                          
000035ad  finddevice                    
00003611  __aeabi_i2d                   
00003611  __floatsidf                   
0000363d  unlink                        
00003691  lseek                         
000036b9  write                         
000036e1  _c_int00_noargs               
00003709  strspn                        
00003731  USART_Init                    
00003755  __aeabi_lmul                  
00003755  __muldi3                      
00003779  puts                          
0000379d  strcspn                       
000037c1  memccpy                       
000037e5  fputc                         
00003805  __aeabi_llsl                  
00003805  __ashldi3                     
00003825  SYSCFG_DL_SYSTICK_init        
00003841  SYSCFG_DL_init                
00003879  SYSCFG_DL_GPIO_init           
000038a9  __aeabi_uldivmod              
000038d1  DL_UART_setClockConfig        
000038e3  __TI_decompress_none          
000038f5  wcslen                        
00003905  __TI_zero_init                
00003925  __aeabi_memset                
00003925  __aeabi_memset4               
00003925  __aeabi_memset8               
0000395d  malloc                        
00003969  DL_Common_delayCycles         
000039c1  __aeabi_errno_addr            
000039c9  __aeabi_memcpy                
000039c9  __aeabi_memcpy4               
000039c9  __aeabi_memcpy8               
000039e1  fseek                         
000039e9  remove                        
000039f1  Reset_Handler                 
000039f5  _system_pre_init              
000039f8  C$$EXIT                       
000039f9  abort                         
00003a00  __aeabi_ctype_table_          
00003a00  __aeabi_ctype_table_C         
00003d04  __TI_Handler_Table_Base       
00003d10  __TI_Handler_Table_Limit      
00003d18  __TI_CINIT_Base               
00003d28  __TI_CINIT_Limit              
00003d28  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  _sys_memory                   
20200800  _CIOBUF_                      
20200800  __CIOBUF_                     
20200a20  g_recv_buff_deal              
20200b20  __TI_tmpnams                  
20200bc0  send_buff                     
20200bf2  g_recv_flag                   
20200bf4  Encoder_Now                   
20200c04  Encoder_Offset                
20200c14  g_Speed                       
20200c24  parmbuf                       
20200c30  _ftable                       
20200d20  recv0_buff                    
20200da0  _device                       
20200e18  _stream                       
20200e68  __TI_cleanup_ptr              
20200e6c  __TI_dtors_ptr                
20200e70  __TI_ft_end                   
20200e74  __aeabi_errno                 
20200e78  _lock                         
20200e7c  _unlock                       
20200e88  recv0_length                  
20200e8a  recv0_flag                    
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[221 symbols]
